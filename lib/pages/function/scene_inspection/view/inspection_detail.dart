import 'dart:typed_data';
import 'package:bcloud/generated/l10n.dart';
import 'package:bcloud/pages/function/scene_inspection/view/inspection_detail_bottom.dart';
import 'package:bcloud/pages/function/scene_inspection/view/inspection_switch_widget.dart';
import 'package:bcloud/pages/function/scene_inspection/model/inspection_abnormal_device.dart';
import 'package:bcloud/pages/function/scene_inspection/view/inspection_detail_top_left_cell.dart';
import 'package:bcloud/pages/function/scene_inspection/view/inspection_detail_top_right_cell.dart';
import 'package:bcloud/pages/function/scene_inspection/view/inspection_detail_top_right_cell_tf.dart';
import 'package:bcloud/pages/function/video_inspection/view/tool_bar_center_title_right_close.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/app_extension.dart';
import 'package:bcloud/widget/cell/icon_title_cell.dart';
import 'package:bcloud/widget/rounded_button.dart';
import 'package:bcloud/widget/xb_bs_title.dart';
import 'package:bcloud/widget/xb_clip_r_rect.dart';

import 'package:bcloud/widget/xb_image_browser/xb_image_browser.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xb_scaffold/xb_scaffold.dart';
import '../model/inspection_detail_model.dart';

typedef InspectionDetailRemarkChanged = void Function(
    {required String remark, required XBSectionIndexPath indexPath});

typedef InspectionDetailPassChanged = void Function(
    {required bool pass, required XBSectionIndexPath indexPath});

typedef InspectionDetailOnDeleteImg = void Function(
    {required XBSectionIndexPath indexPath, required int imgIndex});

typedef InspectionDetailOnEditImg = void Function(
    {XBSectionIndexPath indexPath, Uint8List editedImg, int deviceIndex});

class InspectionDetail extends StatelessWidget {
  final InspectionDetailModel model;

  /// 点击了考评类
  final ValueChanged<int> onTapSection;

  /// 点击了考评项
  final ValueChanged<XBSectionIndexPath> onTapIndexPath;

  /// dialog text change
  final ValueChanged<XBSectionIndexPath> onDialogTextChanged;

  /// 备注发生改变
  final InspectionDetailRemarkChanged onRemarkChanged;

  /// 合格或者不合格发生改变
  final InspectionDetailPassChanged onPassChanged;

  /// 点击了截图
  final ValueChanged<XBSectionIndexPath> onTapSnip;

  /// 删除图片
  final InspectionDetailOnDeleteImg onDeleteImg;

  /// 编辑图片
  final InspectionDetailOnEditImg? onEditImage;

  /// value1: 考评类序号
  /// value2: 考评项序号
  final XBValueChanged2<int, int>? onTapEvent;

  /// 禁止输入备注
  final bool disableInputRemark;

  /// 禁止通过或者不通过操作
  final bool disableChangePass;

  /// 禁止操作图片
  final bool disableChangeImg;

  /// 考评类只显示总数
  final bool leftOnlyTotal;

  /// 最多上传图片数量
  final int maxImgCount;

  /// 是否展示图片和文字切换按钮
  final bool showImgTextSwitch;

  /// 错误的设备信息
  final List<InspectionAbnormalDevice> errDevice;

  const InspectionDetail(
      {required this.model,
      required this.onTapSection,
      required this.onTapIndexPath,
      required this.onDialogTextChanged,
      required this.onRemarkChanged,
      required this.onPassChanged,
      required this.onTapSnip,
      required this.onDeleteImg,
      required this.errDevice,
      this.onEditImage,
      this.disableInputRemark = false,
      this.disableChangePass = false,
      this.disableChangeImg = false,
      this.leftOnlyTotal = false,
      this.showImgTextSwitch = true,
      this.maxImgCount = 10000,
      this.onTapEvent,
      super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFFF8F9FA),
            Color(0xFFFFFFFF),
            Color(0xFFF1F3F4),
          ],
          stops: [0.0, 0.5, 1.0],
        ),
      ),
      child: Column(
        children: [
          top(context),
          bottom(),
        ],
      ),
    );
  }

  Widget top(BuildContext context) {
    return Expanded(
      child: Container(
        margin: EdgeInsets.all(8.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(8),
              blurRadius: 20,
              offset: const Offset(0, 4),
              spreadRadius: 2,
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(16.r),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(width: 108.w, child: topLeft(context)),
              Expanded(child: topRight())
            ],
          ),
        ),
      ),
    );
  }

  int get selectedIndex {
    return model.sections.indexWhere((element) => element.isSelected ?? false);
  }

  Widget topLeft(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            const Color(0xFFF8F9FA),
            Colors.white,
          ],
        ),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16.r),
          bottomLeft: Radius.circular(16.r),
        ),
        border: Border(
          right: BorderSide(
            color: Colors.grey.withAlpha(20),
            width: 1,
          ),
        ),
      ),
      child: MediaQuery.removePadding(
        removeTop: true,
        removeBottom: true,
        context: context,
        child: ListView.builder(
          physics: const BouncingScrollPhysics(),
          shrinkWrap: true,
          itemCount: model.sections.length,
          itemBuilder: (context, index) {
            // if (index < model.sections.length) {
            return _buildEnhancedLeftCell(index);
            // }
            // return _buildLeftBottomDecoration();
          },
        ),
      ),
    );
  }

  Widget _buildEnhancedLeftCell(int index) {
    final isSelected = selectedIndex == index;
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
      decoration: BoxDecoration(
        gradient: isSelected
            ? LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  const Color(0xFF2196F3).withAlpha(20),
                  const Color(0xFF00BCD4).withAlpha(10),
                ],
              )
            : null,
        // borderRadius: BorderRadius.circular(12.r),
        border: isSelected
            ? Border.all(
                color: const Color(0xFF2196F3).withAlpha(60),
                width: 2,
              )
            : null,
        boxShadow: isSelected
            ? [
                BoxShadow(
                  color: const Color(0xFF2196F3).withAlpha(20),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ]
            : null,
      ),
      child: InspectionDetailTopLeftCell(
        onlyTotal: leftOnlyTotal,
        model: model.sections[index],
        needTopRadius: selectedIndex == index - 1,
        needBottomRadius: selectedIndex == index + 1,
        onTap: () {
          onTapSection(index);
        },
      ),
    );
  }

  Widget _buildLeftBottomDecoration() {
    return Container(
      height: 16.h,
      margin: EdgeInsets.symmetric(horizontal: 4.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            colors.viewBG.withAlpha(100),
            colors.viewBG,
          ],
        ),
        borderRadius: BorderRadius.only(
          topRight: Radius.circular(
              selectedIndex == model.sections.length - 1 ? 12.r : 0),
          bottomLeft: Radius.circular(12.r),
          bottomRight: Radius.circular(12.r),
        ),
      ),
    );
  }

  Widget topRight() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.white,
            const Color(0xFFFAFBFC),
          ],
        ),
        borderRadius: BorderRadius.only(
          topRight: Radius.circular(16.r),
          bottomRight: Radius.circular(16.r),
        ),
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
        child: ListView.separated(
          physics: const BouncingScrollPhysics(),
          itemCount: model.items.length + 1,
          itemBuilder: (context, rawIndex) {
            if (rawIndex == 0) {
              return _buildSwitchWidget();
            }
            int index = rawIndex - 1;
            return _buildEnhancedRightCell(index);
          },
          separatorBuilder: (context, index) {
            if (index == 0 && showImgTextSwitch == false) {
              return const SizedBox.shrink();
            }
            return _buildSeparator();
          },
        ),
      ),
    );
  }

  Widget _buildSwitchWidget() {
    if (!showImgTextSwitch) {
      return const SizedBox.shrink();
    }
    return Container(
      margin: EdgeInsets.symmetric(vertical: 8.h),
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF2196F3).withAlpha(10),
            const Color(0xFF00BCD4).withAlpha(5),
          ],
        ),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: const Color(0xFF2196F3).withAlpha(30),
          width: 1,
        ),
      ),
      child: const InspectionSwitchWidget(),
    );
  }

  Widget _buildEnhancedRightCell(int index) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      margin: EdgeInsets.symmetric(vertical: 6.h),
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white,
            const Color(0xFFFAFBFC),
          ],
        ),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: Colors.grey.withAlpha(20),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(5),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InspectionDetailTopRightCell(
        onTapEvent: () {
          onTapEvent?.call(model.selectedSectionIndex(), index);
        },
        maxImgCount: maxImgCount,
        disableChangeImg: disableChangeImg,
        disableChangePass: disableChangePass,
        disableInputRemark: disableInputRemark,
        model: model.items[index],
        onTapDetail: () {
          showDetail(XBSectionIndexPath(model.selectedSectionIndex(), index));
        },
        onTapPass: () {
          onPassChanged(
              pass: true,
              indexPath:
                  XBSectionIndexPath(model.selectedSectionIndex(), index));
        },
        onTapUnpass: () {
          onPassChanged(
              pass: false,
              indexPath:
                  XBSectionIndexPath(model.selectedSectionIndex(), index));
        },
        onRemarkChanged: (value) {
          onRemarkChanged(
              remark: value,
              indexPath:
                  XBSectionIndexPath(model.selectedSectionIndex(), index));
        },
        onTapAdd: () {
          onTapSnip(XBSectionIndexPath(model.selectedSectionIndex(), index));
        },
        onTapDelete: (value) {
          onDeleteImg(
              indexPath:
                  XBSectionIndexPath(model.selectedSectionIndex(), index),
              imgIndex: value);
        },
        onEditImageChanged: (deviceIndex, image) {
          if (onEditImage != null) {
            onEditImage!(
                indexPath:
                    XBSectionIndexPath(model.selectedSectionIndex(), index),
                editedImg: image,
                deviceIndex: deviceIndex);
          }
        },
      ),
    );
  }

  Widget _buildSeparator() {
    return Container(
      height: 1,
      margin: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.transparent,
            Colors.grey.withAlpha(30),
            Colors.transparent,
          ],
        ),
        borderRadius: BorderRadius.circular(0.5),
      ),
    );
  }

  showDetail(XBSectionIndexPath indexPath) {
    final itemTitle = model.itemTitle(indexPath: indexPath);
    const itemTitleStyle = TextStyle();
    final imgList = model.itemExampleImgList(indexPath: indexPath);
    bool isShowImg = imgList.isNotEmpty;
    double countPerRow = 3;
    double gapH = spaces.left;
    double gapV = 10;
    double imgW = (screenW - spaces.left * 2 - gapH * (countPerRow - 1)) / 3;
    double imgH = imgW / 16 * 9;
    int rowSize = imgList.length ~/ countPerRow +
        (imgList.length % countPerRow != 0 ? 1 : 0);

    // xbError("rowSize:$rowSize");

    double totalImgH = !isShowImg ? 0 : rowSize * (imgH + gapV);
    // 文字的底部间隔
    double itemTitleBottom = spaces.leftLess;
    // img的底部间隔
    double imgBottom = isShowImg ? spaces.leftLess - gapV : 0;
    // 输入框的底部间隔
    double tfBottom = spaces.leftLess;
    // 按钮的底部间隔
    double btnBottom = disableChangePass ? 0 : spaces.left;
    actionSheetWidget(
        widget: Scaffold(
          backgroundColor: Colors.transparent,
          resizeToAvoidBottomInset: true,
          body: Container(
            alignment: Alignment.bottomCenter,
            child: Container(
              margin: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.white,
                    const Color(0xFFFAFBFC),
                  ],
                ),
                borderRadius: BorderRadius.circular(20.r),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(20),
                    blurRadius: 30,
                    offset: const Offset(0, 10),
                    spreadRadius: 5,
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 标题栏
                  Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          const Color(0xFF2196F3).withAlpha(10),
                          const Color(0xFF00BCD4).withAlpha(5),
                        ],
                      ),
                    ),
                    child: ToolBarCenterTitleRightClose(
                        title: TR.current.tr_detail,
                        onClose: () {
                          pop();
                        }),
                  ),
                  // 内容区域
                  Padding(
                    padding: EdgeInsets.all(20.w),
                    child: Column(
                      children: [
                        // 标题区域
                        Container(
                          padding: EdgeInsets.all(16.w),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                const Color(0xFFF8F9FA),
                                Colors.white,
                              ],
                            ),
                            borderRadius: BorderRadius.circular(12.r),
                            border: Border.all(
                              color: Colors.grey.withAlpha(20),
                              width: 1,
                            ),
                          ),
                          child: Row(
                            children: [
                              Container(
                                padding: EdgeInsets.all(8.w),
                                decoration: BoxDecoration(
                                  color: const Color(0xFF2196F3),
                                  borderRadius: BorderRadius.circular(8.r),
                                ),
                                child: Icon(
                                  Icons.description_rounded,
                                  color: Colors.white,
                                  size: 20.w,
                                ),
                              ),
                              SizedBox(width: 12.w),
                              Expanded(
                                child: Text(
                                  itemTitle,
                                  style: TextStyle(
                                    fontSize: 16.sp,
                                    fontWeight: FontWeight.w600,
                                    color: const Color(0xFF1A1A1A),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: itemTitleBottom),
                        // 图片网格区域
                        if (isShowImg)
                          _buildImageGrid(
                              imgList, imgW, imgH, countPerRow, gapH, gapV),
                        SizedBox(height: imgBottom),
                        // 备注输入区域
                        _buildRemarkSection(indexPath),
                        SizedBox(height: tfBottom),
                        // 按钮区域
                        if (!disableChangePass) _buildActionButtons(indexPath),
                        SizedBox(height: btnBottom + safeAreaBottom),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        isScrollControlled: true);
  }

  Widget bottom() {
    return InspectionDetailBottom(model: model, children: children);
  }

  List<Widget> children() {
    List<Widget> ret = [
      Expanded(
        child: Padding(
          padding: EdgeInsets.only(left: spaces.left),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Expanded(
                child: Container(
                  // color: colors.randColor,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        height: 25,
                        child: XBBSTitle(
                          big: model.currentScore.toStringAsFixed(0),
                          small: " /${model.totalScore.toStringAsFixed(0)}",
                          mainAxisAlignment: MainAxisAlignment.center,
                          bigFontSize: fontSizes.s16,
                          smallFontSize: fontSizes.s16,
                          smallPaddingBottom: 0,
                          smallColor: colors.black,
                          bigColor: colors.blue,
                        ),
                      ),
                      Text(
                        "${TR.current.tr_getScore}/${TR.current.tr_totalScore}",
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                            fontSize: fontSizes.s12, color: colors.black4C),
                      )
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      Expanded(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              height: 25,
              child: XBBSTitle(
                big: "${model.passCount}",
                small: " ",
                mainAxisAlignment: MainAxisAlignment.center,
                bigFontSize: fontSizes.s16,
              ),
            ),
            Text(
              TR.current.tr_qualifiedItem,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(fontSize: fontSizes.s12, color: colors.black4C),
            )
          ],
        ),
      ),
      Expanded(
        child: Padding(
          padding: EdgeInsets.only(right: spaces.left),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Expanded(
                child: Container(
                  // color: colors.randColor,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        height: 25,
                        child: XBBSTitle(
                          big: "${model.unpassCount}",
                          small: "",
                          mainAxisAlignment: MainAxisAlignment.center,
                          bigFontSize: fontSizes.s16,
                          smallFontSize: fontSizes.s16,
                          smallPaddingBottom: 0,
                          smallColor: colors.black,
                          bigColor: colors.red,
                        ),
                      ),
                      Text(
                        TR.current.tr_unqualifiedItem,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                            fontSize: fontSizes.s12, color: colors.black4C),
                      )
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    ];
    if (errDevice.isNotEmpty) {
      ret.add(
        Expanded(
          child: XBButton(
            onTap: () {
              showErrDevice();
            },
            child: Padding(
              padding: EdgeInsets.only(right: spaces.left),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Expanded(
                    child: Container(
                      // color: colors.randColor,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Container(
                            height: 25,
                            alignment: Alignment.center,
                            child: XBImage(
                              images.icon_inspection_device_err,
                              width: 18,
                            ),
                          ),
                          Text(
                            TR.current.tr_abnormalDevice,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                                fontSize: fontSizes.s12, color: colors.black4C),
                          )
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    }
    return ret;
  }

  showErrDevice() {
    actionSheetWidget(
        widget: ClipRRect(
      borderRadius: BorderRadius.circular(6),
      child: Container(
        color: colors.white,
        child: ListView.builder(
          shrinkWrap: true,
          itemCount: errDevice.length + 2,
          itemBuilder: (context, index) {
            if (index == 0) {
              return ToolBarCenterTitleRightClose(
                  title: TR.current.tr_abnormalDevice,
                  onClose: () {
                    pop();
                  });
            } else if (index == 1) {
              return Padding(
                padding: EdgeInsets.only(
                    left: spaces.left,
                    right: spaces.left,
                    bottom: spaces.leftLess),
                child: Text(TR.current.tr_abnormalDeviceTip),
              );
            } else {
              int realIndex = index - 2;
              InspectionAbnormalDevice device = errDevice[realIndex];
              String title =
                  "${device.deviceName ?? ""}（${(device.abnormalTypeStr ?? "")}）";
              return Padding(
                padding: EdgeInsets.only(
                    left: spaces.left,
                    right: spaces.left,
                    bottom: spaces.left +
                        ((realIndex == errDevice.length - 1)
                            ? safeAreaBottom
                            : 0)),
                child: IconTitleCell(
                    height: null,
                    minHeight: 25,
                    iconW: 24,
                    iconH: 24,
                    gap: 10,
                    icon: images.icon_video_surveillance,
                    title: title),
              );
            }
          },
        ),
      ),
    ));
  }

  Widget _buildImageGrid(List<String> imgList, double imgW, double imgH,
      double countPerRow, double gapH, double gapV) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFFF8F9FA),
            Colors.white,
          ],
        ),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: Colors.grey.withAlpha(20),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.photo_library_rounded,
                color: const Color(0xFF2196F3),
                size: 20.w,
              ),
              SizedBox(width: 8.w),
              Text(
                '参考图片',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF1A1A1A),
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          Wrap(
            children: List.generate(imgList.length, (index) {
              final imagePath = imgList[index];
              return Padding(
                padding: EdgeInsets.only(
                  right: index % countPerRow != countPerRow - 1 ? gapH : 0,
                  bottom: gapV,
                ),
                child: XBButton(
                  onTap: () {
                    push(XBImageBrowser(imgItems: [
                      XBImageBrowserItem(
                          img: imagePath, type: XBImageBrowserItemType.net)
                    ]));
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8.r),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withAlpha(10),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8.r),
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              colors.viewBG,
                              colors.viewBG.withAlpha(200),
                            ],
                          ),
                        ),
                        child: XBImage(
                          imagePath,
                          width: imgW,
                          height: imgH,
                          fit: BoxFit.fill,
                        ),
                      ),
                    ),
                  ),
                ),
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildRemarkSection(XBSectionIndexPath indexPath) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFFF8F9FA),
            Colors.white,
          ],
        ),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: Colors.grey.withAlpha(20),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.edit_note_rounded,
                color: const Color(0xFF2196F3),
                size: 20.w,
              ),
              SizedBox(width: 8.w),
              Text(
                '备注信息',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF1A1A1A),
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          XBDisable(
            disable: disableInputRemark,
            child: InspectionDetailTopRightCellTF(
              initValue: model.remark(indexPath: indexPath),
              onChanged: (remark) {
                onRemarkChanged(remark: remark, indexPath: indexPath);
                onDialogTextChanged(indexPath);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(XBSectionIndexPath indexPath) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFFF8F9FA),
            Colors.white,
          ],
        ),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: Colors.grey.withAlpha(20),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.white,
                    const Color(0xFFF8F9FA),
                  ],
                ),
                borderRadius: BorderRadius.circular(12.r),
                border: Border.all(
                  color: const Color(0xFFFF5722).withAlpha(60),
                  width: 1.5,
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFFFF5722).withAlpha(20),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(12.r),
                  onTap: () {
                    onPassChanged(pass: false, indexPath: indexPath);
                    pop();
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 14.h),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.close_rounded,
                          color: const Color(0xFFFF5722),
                          size: 20.w,
                        ),
                        SizedBox(width: 8.w),
                        Text(
                          TR.current.tr_unqualified,
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w600,
                            color: const Color(0xFFFF5722),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [
                    Color(0xFF4CAF50),
                    Color(0xFF66BB6A),
                  ],
                ),
                borderRadius: BorderRadius.circular(12.r),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF4CAF50).withAlpha(60),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(12.r),
                  onTap: () {
                    onPassChanged(pass: true, indexPath: indexPath);
                    pop();
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 14.h),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.check_rounded,
                          color: Colors.white,
                          size: 20.w,
                        ),
                        SizedBox(width: 8.w),
                        Text(
                          TR.current.tr_qualified,
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
