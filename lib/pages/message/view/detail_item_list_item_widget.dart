import 'package:bcloud/api/device_tree_api.dart';
import 'package:bcloud/generated/l10n.dart';
import 'package:bcloud/pages/function/event_center/model/roi_param.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/app_extension.dart';
import 'package:bcloud/utils/video_scale_util/video_scale_util.dart';
import 'package:bcloud/widget/xb_img_area_mask_widget/xb_img_area_mask_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xb_scaffold/xb_scaffold.dart';

class DetailItemListItemWidget extends StatefulWidget {
  final MessageAIModel messageAIModel;
  final VoidCallback onTapItem;
  final Size? imgRawSize;
  const DetailItemListItemWidget(
      {required this.messageAIModel,
      required this.onTapItem,
      this.imgRawSize,
      Key? key})
      : super(key: key);

  @override
  State<DetailItemListItemWidget> createState() =>
      _DetailItemListItemWidgetState();
}

class _DetailItemListItemWidgetState extends State<DetailItemListItemWidget> {
  bool get isHasImg {
    final detailData = widget.messageAIModel.detailData;
    if (detailData == null || detailData.isEmpty) {
      return false;
    }
    return detailData[0].image != null;
  }

  int _maskKey = 0;

  Widget _loadImage() {
    return isHasImg
        ? Container(
            alignment: Alignment.center,
            height: _imgH,
            width: _imgW,
            child: XBImgAreaMaskWidget(
              // key: ValueKey(_maskKey),
              image: widget.messageAIModel.detailData![0].image,
              boxs: widget.messageAIModel.detailData![0].objects ?? [],
              roiPoints:
                  RoiParam.roiPoints(widget.messageAIModel.roiParam ?? ""),
              displayHeight: _imgH,
              displayWidth: _imgW,
              imgRawSize: widget.imgRawSize,
              lineWidth: 1.5,
              whScale:
                  VideoScaleUtil.getVideoScale(widget.messageAIModel.deviceId),
            ),
          )
        : XBImage(
            images.icon_no_image,
            width: _imgW,
            height: _imgH,
            fit: BoxFit.fill,
          );
  }

  double get _imgW => 120.w;
  double get _imgH => _imgW / 16 * 9;

  @override
  void didUpdateWidget(covariant DetailItemListItemWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    _maskKey++;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final isSelected = widget.messageAIModel.isSelect;
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      curve: Curves.easeInOut,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: isSelected
              ? [
                  const Color(0xFF2196F3).withAlpha(8),
                  const Color(0xFF00BCD4).withAlpha(5),
                  Colors.white,
                ]
              : [
                  Colors.white,
                  const Color(0xFFFAFBFC),
                  Colors.white,
                ],
          stops: const [0.0, 0.5, 1.0],
        ),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: isSelected
              ? const Color(0xFF2196F3).withAlpha(40)
              : Colors.grey.withAlpha(15),
          width: isSelected ? 1.5 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: isSelected
                ? const Color(0xFF2196F3).withAlpha(15)
                : Colors.black.withAlpha(5),
            blurRadius: isSelected ? 12 : 8,
            offset: const Offset(0, 3),
            spreadRadius: isSelected ? 1 : 0,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12.r),
          onTap: () {
            widget.onTapItem.call();
          },
          child: Row(
            children: [
              // 左侧状态指示器
              _buildStatusIndicator(),
              SizedBox(width: 12.w),

              // 主要内容区域
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 告警名称
                    _buildAlarmNameSection(),
                    SizedBox(height: 8.h),

                    // 节点信息
                    _buildNodeSection(),
                    SizedBox(height: 6.h),

                    // 时间信息
                    _buildTimeSection(),
                    SizedBox(height: 8.h),

                    // 底部标签区域
                    _buildTagsSection(),
                  ],
                ),
              ),

              SizedBox(width: 16.w),

              // 右侧图片区域
              _buildImageSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusIndicator() {
    final statusColor = _getAlarmLevelColor();

    return Container(
      width: 4.w,
      height: 60.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(2.r),
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            statusColor,
            statusColor.withAlpha(150),
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: statusColor.withAlpha(60),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
    );
  }

  Widget _buildAlarmNameSection() {
    final isSelected = widget.messageAIModel.isSelect;
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(6.w),
          decoration: BoxDecoration(
            color: _getAlarmLevelColor().withAlpha(20),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Icon(
            _getAlarmIcon(),
            size: 16.w,
            color: _getAlarmLevelColor(),
          ),
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: Text(
            widget.messageAIModel.alarmName ?? '',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: isSelected
                  ? const Color(0xFF2196F3)
                  : const Color(0xFF1A1A1A),
            ),
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
        ),
        if (isSelected)
          Container(
            padding: EdgeInsets.all(4.w),
            decoration: BoxDecoration(
              color: const Color(0xFF2196F3),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Icon(
              Icons.check_rounded,
              size: 12.w,
              color: Colors.white,
            ),
          ),
      ],
    );
  }

  Widget _buildNodeSection() {
    return Row(
      children: [
        Icon(
          Icons.account_tree_rounded,
          size: 14.w,
          color: const Color(0xFF666666),
        ),
        SizedBox(width: 6.w),
        Text(
          TR.current.tr_Node,
          style: TextStyle(
            fontSize: 13.sp,
            color: const Color(0xFF666666),
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(width: 4.w),
        Expanded(
          child: Text(
            widget.messageAIModel.levelName ?? '',
            style: TextStyle(
              fontSize: 13.sp,
              color: const Color(0xFF1A1A1A),
              fontWeight: FontWeight.w500,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildTimeSection() {
    return Row(
      children: [
        Icon(
          Icons.access_time_rounded,
          size: 14.w,
          color: const Color(0xFF666666),
        ),
        SizedBox(width: 6.w),
        Text(
          TR.current.tr_time,
          style: TextStyle(
            fontSize: 13.sp,
            color: const Color(0xFF666666),
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(width: 4.w),
        Expanded(
          child: Text(
            _formatTime(widget.messageAIModel.alarmTime ?? ''),
            style: TextStyle(
              fontSize: 13.sp,
              color: const Color(0xFF1A1A1A),
              fontWeight: FontWeight.w500,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildTagsSection() {
    return Row(
      children: [
        _buildAlarmLevelTag(),
        SizedBox(width: 8.w),
        if (widget.messageAIModel.deviceName?.isNotEmpty == true)
          _buildDeviceTag(),
      ],
    );
  }

  Widget _buildAlarmLevelTag() {
    final levelColor = _getAlarmLevelColor();
    final levelText = _getAlarmLevelText();

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: levelColor.withAlpha(20),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: levelColor.withAlpha(60),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 4.w,
            height: 4.w,
            decoration: BoxDecoration(
              color: levelColor,
              shape: BoxShape.circle,
            ),
          ),
          SizedBox(width: 4.w),
          Text(
            levelText,
            style: TextStyle(
              fontSize: 10.sp,
              color: levelColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDeviceTag() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: const Color(0xFF4CAF50).withAlpha(20),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: const Color(0xFF4CAF50).withAlpha(60),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.devices_rounded,
            size: 10.w,
            color: const Color(0xFF4CAF50),
          ),
          SizedBox(width: 4.w),
          Text(
            widget.messageAIModel.deviceName ?? '',
            style: TextStyle(
              fontSize: 10.sp,
              color: const Color(0xFF4CAF50),
              fontWeight: FontWeight.w500,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildImageSection() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(10),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12.r),
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                colors.viewBG,
                colors.viewBG.withAlpha(200),
              ],
            ),
          ),
          child: _loadImage(),
        ),
      ),
    );
  }

  Color _getAlarmLevelColor() {
    final levelName = widget.messageAIModel.levelName?.toLowerCase() ?? '';
    if (levelName.contains('高') || levelName.contains('high')) {
      return const Color(0xFFFF5722);
    } else if (levelName.contains('中') || levelName.contains('medium')) {
      return const Color(0xFFFF9800);
    } else {
      return const Color(0xFF4CAF50);
    }
  }

  String _getAlarmLevelText() {
    final levelName = widget.messageAIModel.levelName?.toLowerCase() ?? '';
    if (levelName.contains('高') || levelName.contains('high')) {
      return '高危';
    } else if (levelName.contains('中') || levelName.contains('medium')) {
      return '中危';
    } else {
      return '低危';
    }
  }

  IconData _getAlarmIcon() {
    final levelName = widget.messageAIModel.levelName?.toLowerCase() ?? '';
    if (levelName.contains('高') || levelName.contains('high')) {
      return Icons.warning_rounded;
    } else if (levelName.contains('中') || levelName.contains('medium')) {
      return Icons.error_outline_rounded;
    } else {
      return Icons.info_outline_rounded;
    }
  }

  String _formatTime(String timeStr) {
    if (timeStr.isEmpty) return '';
    try {
      // 如果时间字符串包含日期，只显示时间部分
      if (timeStr.contains(' ')) {
        return timeStr.split(' ').last;
      }
      return timeStr;
    } catch (e) {
      return timeStr;
    }
  }
}
