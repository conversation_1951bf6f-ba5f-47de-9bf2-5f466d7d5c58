import 'package:bcloud/generated/l10n.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/app_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xb_scaffold/xb_scaffold.dart';

import 'message_detail_page_vm.dart';

class MessageDetailItemTextViewPage extends StatelessWidget {
  final MessageDetailPageVM vm;

  const MessageDetailItemTextViewPage(
    this.vm, {
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            colors.viewBG,
            colors.viewBG,
            colors.viewBG,
          ],
          stops: const [0.0, 0.5, 1.0],
        ),
      ),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        margin: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(20),
              bottomRight: Radius.circular(20)),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white,
              const Color(0xFFFAFBFC),
              Colors.white,
            ],
            stops: const [0.0, 0.5, 1.0],
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(8),
              blurRadius: 20,
              offset: const Offset(0, 8),
              spreadRadius: 2,
            ),
            BoxShadow(
              color: const Color(0xFF2196F3).withAlpha(5),
              blurRadius: 30,
              offset: const Offset(0, 4),
              spreadRadius: 1,
            ),
          ],
          border: Border.all(
            color: Colors.grey.withAlpha(20),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            // 顶部装饰条
            Container(
              height: 4.h,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20.r),
                  topRight: Radius.circular(20.r),
                ),
                gradient: const LinearGradient(
                  colors: [
                    Color(0xFF2196F3),
                    Color(0xFF00BCD4),
                    Color(0xFF4CAF50),
                  ],
                ),
              ),
            ),
            Expanded(
              child: SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                child: Padding(
                  padding: EdgeInsets.all(20.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 标题区域
                      _buildHeaderSection(),
                      SizedBox(height: 24.h),

                      // 详细信息区域
                      _buildDetailSection(),

                      SizedBox(height: 24.h),

                      // 分割线
                      _buildDivider(),

                      SizedBox(height: 20.h),

                      // 图例区域
                      _buildLegendSection(),

                      SizedBox(height: 16.h),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderSection() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF2196F3).withAlpha(10),
            const Color(0xFF00BCD4).withAlpha(5),
          ],
        ),
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color: const Color(0xFF2196F3).withAlpha(30),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: const Color(0xFF2196F3),
              borderRadius: BorderRadius.circular(12.r),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF2196F3).withAlpha(60),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Icon(
              Icons.info_outline_rounded,
              color: Colors.white,
              size: 24.w,
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '告警详情',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF1A1A1A),
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  '查看详细的告警信息和检测结果',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: const Color(0xFF666666),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailSection() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(5),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: Colors.grey.withAlpha(20),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          _buildEnhancedItem(
            TR.current.tr_alarmType,
            vm.txtAlarmName,
            Icons.warning_amber_rounded,
            const Color(0xFFFF5722),
          ),
          _buildItemDivider(),
          _buildEnhancedItem(
            TR.current.tr_alarmTime,
            vm.txtAlarmTime,
            Icons.access_time_rounded,
            const Color(0xFF2196F3),
          ),
          _buildItemDivider(),
          _buildEnhancedItem(
            TR.current.tr_DeviceName,
            vm.txtDeviceName,
            Icons.devices_rounded,
            const Color(0xFF4CAF50),
          ),
          _buildItemDivider(),
          _buildEnhancedItem(
            TR.current.tr_nodeInfo,
            vm.txtLevelName,
            Icons.account_tree_rounded,
            const Color(0xFF9C27B0),
          ),
          if (vm.showTargetInfo) ...[
            _buildItemDivider(),
            _buildEnhancedItem(
              TR.current.tr_targetInformation,
              vm.targetInfo,
              Icons.gps_fixed_rounded,
              const Color(0xFFFF9800),
            ),
          ],
          if (vm.showScore) ...[
            _buildItemDivider(),
            _buildEnhancedItem(
              TR.current.tr_confidenceLevel,
              vm.txtScore,
              Icons.analytics_rounded,
              const Color(0xFF00BCD4),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildEnhancedItem(
      String title, String subTitle, IconData icon, Color iconColor) {
    return Padding(
      padding: EdgeInsets.all(16.w),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(8.w),
            decoration: BoxDecoration(
              color: iconColor.withAlpha(20),
              borderRadius: BorderRadius.circular(10.r),
            ),
            child: Icon(
              icon,
              color: iconColor,
              size: 20.w,
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 13.sp,
                    color: const Color(0xFF666666),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  subTitle,
                  maxLines: 2,
                  style: TextStyle(
                    fontSize: 15.sp,
                    color: const Color(0xFF1A1A1A),
                    fontWeight: FontWeight.w600,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildItemDivider() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      height: 1,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.transparent,
            Colors.grey.withAlpha(30),
            Colors.transparent,
          ],
        ),
      ),
    );
  }

  Widget _buildDivider() {
    return Container(
      height: 2.h,
      margin: EdgeInsets.symmetric(horizontal: 8.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(1.r),
        gradient: const LinearGradient(
          colors: [
            Color(0xFF2196F3),
            Color(0xFF00BCD4),
            Color(0xFF4CAF50),
          ],
        ),
      ),
    );
  }

  Widget _buildLegendSection() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFFF8F9FA),
            Colors.white,
          ],
        ),
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color: Colors.grey.withAlpha(20),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.legend_toggle_rounded,
                color: const Color(0xFF666666),
                size: 20.w,
              ),
              SizedBox(width: 8.w),
              Text(
                '检测框图例',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF1A1A1A),
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          Wrap(
            spacing: 12.w,
            runSpacing: 12.h,
            children: [
              _buildEnhancedBoxContainer(
                  const Color(0xFF36B876), TR.current.tr_detectionBox),
              _buildEnhancedBoxContainer(
                  const Color(0xFFFF7955), TR.current.tr_targetBox),
              _buildEnhancedBoxContainer(
                  const Color(0xFF4796F2), TR.current.tr_endSidePassengerBox),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEnhancedBoxContainer(Color color, String title) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: color.withAlpha(10),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: color.withAlpha(60),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: color.withAlpha(20),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 12.w,
            height: 12.w,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(3.r),
              boxShadow: [
                BoxShadow(
                  color: color.withAlpha(60),
                  blurRadius: 4,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
          ),
          SizedBox(width: 8.w),
          Text(
            title,
            style: TextStyle(
              fontSize: 13.sp,
              color: const Color(0xFF1A1A1A),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
