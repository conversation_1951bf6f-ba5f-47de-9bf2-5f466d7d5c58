import 'package:bcloud/api/device_tree_api.dart';
import 'package:bcloud/generated/l10n.dart';
import 'package:bcloud/pages/message/view/detail_item_list_item_widget.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/app_extension.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/xb_refresh_list_view.dart';
import 'package:bcloud/widget/bc_refresh.dart';
import 'package:flutter/material.dart';
import 'package:xb_scaffold/xb_scaffold.dart';
import 'message_detail_page_vm.dart';

class MessageDetailItemListViewPage extends StatefulWidget {
  final MessageDetailPageVM vm;

  const MessageDetailItemListViewPage(
    this.vm, {
    Key? key,
  }) : super(key: key);

  @override
  State<MessageDetailItemListViewPage> createState() =>
      _MessageDetailItemListViewPageState();
}

class _MessageDetailItemListViewPageState
    extends State<MessageDetailItemListViewPage>
    with AutomaticKeepAliveClientMixin {
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Container(
      color: colors.white,
      child: Column(
        children: [
          Expanded(
              child: BCRefresh(
                  controller: widget.vm.refreshController,
                  needLoadMore: true,
                  onRefresh: () {
                    widget.vm.refresh();
                  },
                  onLoadMore: () {
                    widget.vm.loadMore();
                  },
                  child: widget.vm.isNotEmpty
                      ? ListView.separated(
                          padding: EdgeInsets.symmetric(
                              horizontal: spaces.left,
                              vertical: spaces.leftLess),
                          itemCount: widget.vm.itemListCount,
                          itemBuilder: (ctx, index) {
                            MessageAIModel model = widget.vm.itemList[index];
                            return DetailItemListItemWidget(
                              messageAIModel: model,
                              onTapItem: () {
                                widget.vm.onTapItem(model, index);
                              },
                            );
                          },
                          separatorBuilder: (context, index) {
                            return SizedBox(height: spaces.left);
                          })
                      : Padding(
                          padding: EdgeInsets.only(
                              left: spaces.leftLarge, right: spaces.leftLarge),
                          child: XBRefreshListView.noData(
                              tip: TR.current.tr_NoData),
                        ))),
        ],
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
