import 'package:bcloud/api/device_tree_api.dart';
import 'package:bcloud/generated/l10n.dart';
import 'package:bcloud/pages/message/view/detail_item_list_item_widget.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/app_extension.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/xb_refresh_list_view.dart';
import 'package:bcloud/widget/bc_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xb_scaffold/xb_scaffold.dart';
import 'dart:math' as math;
import 'message_detail_page_vm.dart';

class MessageDetailItemListViewPage extends StatefulWidget {
  final MessageDetailPageVM vm;

  const MessageDetailItemListViewPage(
    this.vm, {
    Key? key,
  }) : super(key: key);

  @override
  State<MessageDetailItemListViewPage> createState() =>
      _MessageDetailItemListViewPageState();
}

class _MessageDetailItemListViewPageState
    extends State<MessageDetailItemListViewPage>
    with AutomaticKeepAliveClientMixin {
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            const Color(0xFFF8F9FA),
            const Color(0xFFFFFFFF),
            const Color(0xFFF1F3F4),
          ],
          stops: const [0.0, 0.5, 1.0],
        ),
      ),
      child: Column(
        children: [
          // 顶部统计信息栏
          _buildStatsHeader(),

          // 主要内容区域
          Expanded(
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 8.w),
              child: BCRefresh(
                controller: widget.vm.refreshController,
                needLoadMore: true,
                onRefresh: () {
                  widget.vm.refresh();
                },
                onLoadMore: () {
                  widget.vm.loadMore();
                },
                child: widget.vm.isNotEmpty
                    ? _buildListView()
                    : _buildEmptyState(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsHeader() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF2196F3).withAlpha(10),
            const Color(0xFF00BCD4).withAlpha(5),
          ],
        ),
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color: const Color(0xFF2196F3).withAlpha(30),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(5),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(10.w),
            decoration: BoxDecoration(
              color: const Color(0xFF2196F3),
              borderRadius: BorderRadius.circular(12.r),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF2196F3).withAlpha(60),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Icon(
              Icons.list_alt_rounded,
              color: Colors.white,
              size: 20.w,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '告警记录列表',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF1A1A1A),
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
            decoration: BoxDecoration(
              color: const Color(0xFF4CAF50).withAlpha(20),
              borderRadius: BorderRadius.circular(20.r),
              border: Border.all(
                color: const Color(0xFF4CAF50).withAlpha(60),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 6.w,
                  height: 6.w,
                  decoration: BoxDecoration(
                    color: const Color(0xFF4CAF50),
                    shape: BoxShape.circle,
                  ),
                ),
                SizedBox(width: 6.w),
                Text(
                  '实时更新',
                  style: TextStyle(
                    fontSize: 11.sp,
                    color: const Color(0xFF4CAF50),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildListView() {
    return ListView.separated(
      padding: EdgeInsets.symmetric(
        horizontal: 4.w,
        vertical: 8.h,
      ),
      physics: const BouncingScrollPhysics(),
      itemCount: widget.vm.itemListCount,
      itemBuilder: (ctx, index) {
        MessageAIModel model = widget.vm.itemList[index];
        return _buildEnhancedListItem(model, index);
      },
      separatorBuilder: (context, index) {
        return SizedBox(height: 12.h);
      },
    );
  }

  Widget _buildEnhancedListItem(MessageAIModel model, int index) {
    final isSelected = model.isSelect;
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      margin: EdgeInsets.symmetric(horizontal: 4.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: isSelected
              ? [
                  const Color(0xFF2196F3).withAlpha(15),
                  const Color(0xFF00BCD4).withAlpha(10),
                  Colors.white,
                ]
              : [
                  Colors.white,
                  const Color(0xFFFAFBFC),
                  Colors.white,
                ],
          stops: const [0.0, 0.5, 1.0],
        ),
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color: isSelected
              ? const Color(0xFF2196F3).withAlpha(60)
              : Colors.grey.withAlpha(20),
          width: isSelected ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: isSelected
                ? const Color(0xFF2196F3).withAlpha(20)
                : Colors.black.withAlpha(8),
            blurRadius: isSelected ? 15 : 10,
            offset: const Offset(0, 4),
            spreadRadius: isSelected ? 2 : 1,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16.r),
          onTap: () {
            widget.vm.onTapItem(model, index);
          },
          child: Container(
            // padding: EdgeInsets.all(16.w),
            child: Column(
              children: [
                // 顶部装饰条
                if (isSelected)
                  Container(
                    height: 3.h,
                    margin: EdgeInsets.only(bottom: 12.h),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(2.r),
                      gradient: const LinearGradient(
                        colors: [
                          Color(0xFF2196F3),
                          Color(0xFF00BCD4),
                        ],
                      ),
                    ),
                  ),

                // 原有的DetailItemListItemWidget
                DetailItemListItemWidget(
                  messageAIModel: model,
                  onTapItem: () {
                    widget.vm.onTapItem(model, index);
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Container(
        margin: EdgeInsets.all(40.w),
        padding: EdgeInsets.all(32.w),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.white,
              const Color(0xFFF8F9FA),
            ],
          ),
          borderRadius: BorderRadius.circular(20.r),
          border: Border.all(
            color: Colors.grey.withAlpha(20),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(5),
              blurRadius: 20,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: EdgeInsets.all(20.w),
              decoration: BoxDecoration(
                color: const Color(0xFF2196F3).withAlpha(10),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.inbox_rounded,
                size: 48.w,
                color: const Color(0xFF2196F3),
              ),
            ),
            SizedBox(height: 24.h),
            Text(
              '暂无数据',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF1A1A1A),
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              '当前没有告警记录\n请稍后再试或刷新页面',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14.sp,
                color: const Color(0xFF666666),
                height: 1.5,
              ),
            ),
            SizedBox(height: 24.h),
            GestureDetector(
              onTap: () {
                widget.vm.refresh();
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [
                      Color(0xFF2196F3),
                      Color(0xFF00BCD4),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(25.r),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF2196F3).withAlpha(60),
                      blurRadius: 12,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.refresh_rounded,
                      color: Colors.white,
                      size: 16.w,
                    ),
                    SizedBox(width: 8.w),
                    Text(
                      '刷新',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
