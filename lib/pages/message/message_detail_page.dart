import 'package:bcloud/api/device_tree_api.dart';
import 'package:bcloud/generated/assets.gen.dart';
import 'package:bcloud/utils/xb_direction_util/xb_direction_controller.dart';
import 'package:bcloud/widget/xb_jf_player/xb_media_player_direction.dart';
import 'package:bcloud/widget/xb_progress/xb_progress_line.dart';
import 'package:bcloud/pages/message/message_detail_page_vm.dart';
import 'package:bcloud/pages/message/view/message_detail_pagination_builder.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/app_extension.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/xb_page_over.dart';
import 'package:bcloud/widget/xb_dismiss_tool_bar_widget/xb_dismiss_tool_bar_widget.dart';
import 'package:bcloud/widget/xb_img_area_mask_widget/xb_img_area_mask_widget.dart';
import 'package:bcloud/widget/xb_jf_player/xb_player_cover.dart';
import 'package:bcloud/widget/xb_jf_player/xb_player_cover_def_builder.dart';
import 'package:bcloud/widget/xb_select_cond_widget/xb_select_cond_widget_line.dart';
import 'package:card_swiper/card_swiper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xb_scaffold/xb_scaffold.dart';
import 'message_detail_item_list_view_page.dart';
import 'message_detail_item_text_view_page.dart';

class MessageDetailPage extends XBPageOver<MessageDetailPageVM> {
  final int type;

  ///1 消息列表进入传入model 2 设备直播页面进入传入deviceId 3 通过消息通知进入传入messageId
  final String? deviceId;
  final String? messageId;
  final String? alarmTime;
  final MessageAIModel? model;
  const MessageDetailPage(
      {required this.type,
      this.deviceId,
      this.messageId,
      this.alarmTime,
      this.model,
      super.key});

  @override
  generateVM(BuildContext context) {
    return MessageDetailPageVM(
        context: context,
        type: type,
        deviceId: deviceId,
        messageId: messageId,
        alarmTime: alarmTime);
  }

  @override
  bool needRebuildWhileOrientationChanged(MessageDetailPageVM vm) {
    return true;
  }

  @override
  bool needShowContentFromScreenTop(MessageDetailPageVM vm) {
    return vm.isOrientationH ? true : false;
  }

  @override
  String setTitle(MessageDetailPageVM vm) {
    return vm.title;
  }

  @override
  List<Widget>? actions(MessageDetailPageVM vm) {
    return vm.isDelete
        ? [
            XBButton(
              onTap: () {
                vm.onDelete();
              },
              child: Padding(
                padding: EdgeInsets.only(right: 16.w),
                child: Assets.images.icCommonDeleteAll.svg(
                    width: 24,
                    height: 24,
                    colorFilter:
                        const ColorFilter.mode(Colors.black, BlendMode.srcIn)),
              ),
            ),
          ]
        : null;
  }

  @override
  bool needLoading(MessageDetailPageVM vm) {
    return true;
  }

  @override
  bool needIosGestureBack(MessageDetailPageVM vm) {
    if (vm.isOrientationH) {
      return false;
    } else {
      return true;
    }
  }

  @override
  bool onAndroidPhysicalBack(MessageDetailPageVM vm) {
    if (vm.isOrientationH) {
      vm.changeOrientation();
      return false;
    } else {
      return true;
    }
  }

  @override
  Widget buildPage(MessageDetailPageVM vm, BuildContext context) {
    if (vm.isOrientationH) {
      return _orientationH(vm);
    } else {
      return _orientationV(vm);
    }
  }

  _toolBarImageV(MessageDetailPageVM vm, [double? height, Color? bgColor]) {
    height ??= 50;
    bgColor ??= colors.black.withAlpha(100);
    return XBGradientWidget(
      beginColor: Colors.transparent,
      endColor: colors.black,
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      child: Container(
        height: height,
        // color: bgColor,
        child: Row(
          children: [
            Visibility(
              visible: vm.isHasVideo,
              child: XBButton(
                  onTap: vm.onTapPlayImg,
                  child: Container(
                    color: Colors.transparent,
                    height: height,
                    child: Padding(
                      padding: EdgeInsets.only(
                          left: spaces.left, right: 10, top: 5, bottom: 5),
                      child: XBImage(
                        images.ic_live_func_play,
                        width: 20,
                      ),
                    ),
                  )),
            ),
            Expanded(child: Container()),
            XBButton(
              onTap: vm.onTapDownload,
              child: Container(
                color: Colors.transparent,
                child: Padding(
                  padding: EdgeInsets.only(
                      top: 15,
                      bottom: 15,
                      left: spaces.left,
                      right: spaces.leftLess),
                  child: XBImage(
                    images.icon_download,
                    width: 20,
                  ),
                ),
              ),
            ),
            XBButton(
              onTap: vm.changeOrientation,
              child: Container(
                color: Colors.transparent,
                child: Padding(
                  padding: EdgeInsets.only(
                      top: 15,
                      bottom: 15,
                      left: spaces.leftLess,
                      right: spaces.left),
                  child: XBImage(
                    images.ic_live_func_fullscreen,
                    width: 20,
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  _toolBarVideoV(MessageDetailPageVM vm,
      [double? height, Color? bgColor, bool showImg = true]) {
    height ??= 50;
    bgColor ??= colors.black.withAlpha(100);
    return XBGradientWidget(
      beginColor: Colors.transparent,
      endColor: colors.black,
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      child: Container(
        height: height,
        // color: bgColor,
        child: Column(
          children: [
            Container(
              // color: colors.red,
              child: Padding(
                padding: EdgeInsets.only(left: spaces.left, right: spaces.left),
                child: Row(
                  children: [
                    Text(
                      vm.startTimeShow,
                      style: TextStyle(
                          color: colors.white.withAlpha(180),
                          fontSize: fontSizes.s10),
                    ),
                    const SizedBox(
                      width: 5,
                    ),
                    Expanded(
                        child: Container(
                      color: colors.black.withAlpha(100),
                      child: XBProgressLine(
                        progress: vm.progress,
                        strokeWidth: 2,
                        backgroundColor: colors.white.withAlpha(80),
                        foregroundColor: colors.white,
                      ),
                    )),
                    const SizedBox(
                      width: 5,
                    ),
                    Text(
                      vm.endTimeShow,
                      style: TextStyle(
                          color: colors.white.withAlpha(180),
                          fontSize: fontSizes.s10),
                    )
                  ],
                ),
              ),
            ),
            Expanded(
              child: Container(
                // color: colors.blue,
                child: Row(
                  children: [
                    XBButton(
                        onTap: vm.onTapPlay,
                        child: Container(
                          color: Colors.transparent,
                          // height: height,
                          child: Padding(
                            padding: EdgeInsets.only(
                                left: spaces.left,
                                right: spaces.left,
                                top: 5,
                                bottom: 5),
                            child: XBImage(
                              vm.isPlaying
                                  ? images.ic_live_func_pause
                                  : images.ic_live_func_play,
                              width: 22,
                            ),
                          ),
                        )),
                    Expanded(child: Container()),
                    Visibility(
                      visible: showImg,
                      child: XBButton(
                        onTap: vm.onTapSwitchImg,
                        child: Container(
                          color: Colors.transparent,
                          child: Padding(
                            padding: EdgeInsets.only(
                                top: 5,
                                bottom: 5,
                                left: spaces.left,
                                right: spaces.leftLess),
                            child: XBImage(
                              images.icon_switch_img,
                              width: 20,
                            ),
                          ),
                        ),
                      ),
                    ),
                    XBButton(
                      onTap: vm.changeOrientation,
                      child: Container(
                        color: Colors.transparent,
                        child: Padding(
                          padding: EdgeInsets.only(
                              left: spaces.leftLess,
                              right: spaces.left,
                              top: 5,
                              bottom: 5),
                          child: XBImage(
                            images.ic_live_func_fullscreen,
                            width: 20,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  _orientationV(MessageDetailPageVM vm) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        vm.isShowVideo
            ? XBDismissToolBarWidget(
                duration: 100000,
                bottomBar: _toolBarVideoV(vm),
                child: _video(vm))
            : XBDismissToolBarWidget(
                duration: 50000,
                bottomBar: _toolBarImageV(vm),
                child: _image(vm)),
        topBar(vm),
        Expanded(
            child: PageView(
          allowImplicitScrolling: true,
          // physics: const NeverScrollableScrollPhysics(),
          controller: vm.pageController,
          children: [
            MessageDetailItemTextViewPage(vm),
            MessageDetailItemListViewPage(vm)
          ],
          onPageChanged: (value) {
            vm.onChangeShowState(value);
          },
        )),
      ],
    );
  }

  Widget topBar(MessageDetailPageVM vm) {
    return Container(
      color: colors.white,
      alignment: Alignment.center,
      height: 50,
      child: XBSelectCondWidgetLine(
          selectedIndex: vm.selectedIndex,
          models: vm.titleItems,
          itemGap: 120,
          fontSize: fontSizes.s16,
          lineHeight: 2,
          fontWeight: fontWeights.medium,
          onSelected: vm.onChangeShowState),
    );
  }

  Widget _image(MessageDetailPageVM vm) {
    return AspectRatio(
      aspectRatio: vm.whScale,
      child: Container(
        color: Colors.black,
        child: Swiper(
          onIndexChanged: vm.onImgIndexChanged,
          loop: vm.itemCount > 1,
          autoplay: vm.itemCount > 1,
          itemBuilder: (BuildContext context, int index) {
            return Container(
                color: const Color.fromRGBO(136, 135, 135, 1),
                alignment: Alignment.center,
                child: vm.isExist(index)
                    ? XBImgAreaMaskWidget(
                        key: ValueKey(vm.areaKey),
                        image: vm.detailData(index).image,
                        boxs: vm.detailData(index).objects ?? [],
                        roiPoints: vm.roiPoints(index),
                        displayHeight: screenH,
                        displayWidth: screenW,
                        lineWidth: vm.isOrientationH ? 5 : 3,
                        onGotImgSize: vm.onGotImgSize,
                        isDrawText: true,
                        whScale: vm.whScale,
                      )
                    : XBImage(
                        images.icon_no_image,
                        fit: BoxFit.fill,
                      ));
          },
          itemCount: vm.itemCount,
          // viewportFraction: vm.itemViewportFraction,
          // scale: vm.scale,
          pagination: vm.itemCount > 1
              ? SwiperPagination(
                  margin: const EdgeInsets.only(bottom: 30),
                  builder: MessageDetailPaginationBuilder(
                      size: 10,
                      activeSize: 10,
                      color: colors.line3,
                      activeColor: colors.blue))
              : null,
        ),
      ),
    );
  }

  Widget _video(MessageDetailPageVM vm) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      color: colors.black,
      width: screenW,
      height:
          XBDirectionController.isDirectionV ? screenW / vm.whScale : screenH,
      alignment: Alignment.center,
      child: vm.controllerRecord == null
          ? Container()
          : XBPlayerCover(
              key: vm.playerKey,
              controller: vm.controllerRecord!,
              player: XbMediaPlayerDirection(
                displaySize: Size(
                    screenW,
                    XBDirectionController.isDirectionV
                        ? screenW / vm.whScale
                        : screenH),
                isFill: XBDirectionController.isDirectionV,
                whScale: vm.whScale,
                controller: vm.controllerRecord!.mediaController,
              ),
              errorBuilder: (context) {
                return XBPlayerCoverDefRetry(onRetry: vm.retry);
              },
              retryFailureBuilder: (context) {
                return XBPlayerCoverDefRetry(onRetry: vm.retry);
              }),
    );
  }

  _orientationH(MessageDetailPageVM vm) {
    return Container(
      alignment: Alignment.center,
      color: colors.black,
      child: XBDismissToolBarWidget(
        topBar: Padding(
          padding: EdgeInsets.only(left: 0, top: stateBarH),
          child: SizedBox(
              height: naviBarH,
              child: Container(
                  color: Colors.transparent,
                  alignment: Alignment.centerLeft,
                  child: XBButton(
                    onTap: () {
                      vm.back();
                    },
                    child: Container(
                      color: Colors.transparent,
                      height: naviBarH,
                      width: topBarH,
                      child: Container(
                        alignment: Alignment.center,
                        child: XBImage(
                          images.ic_common_back_white,
                          width: 25,
                          height: 23,
                        ),
                      ),
                    ),
                  ))),
        ),
        bottomBar: vm.isShowVideo ? _toolBarVideoH(vm) : _toolBarH(vm),
        child: vm.isShowVideo ? _video(vm) : _image(vm),
      ),
    );
  }

  Widget _toolBarVideoH(MessageDetailPageVM vm) {
    double height = 55;
    return XBGradientWidget(
      beginColor: Colors.transparent,
      endColor: colors.black,
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      child: _toolBarVideoV(vm, height, Colors.transparent, false),
    );
  }

  Widget _toolBarH(MessageDetailPageVM vm) {
    return XBGradientWidget(
      beginColor: Colors.transparent,
      endColor: colors.black,
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      child: Container(
        // color: colors.black.withAlpha(100),
        height: 50,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(child: Container()),
            XBButton(
              onTap: vm.onTapDownload,
              child: Container(
                color: Colors.transparent,
                child: Padding(
                  padding: EdgeInsets.only(
                      top: 15,
                      bottom: 15,
                      left: spaces.left,
                      right: spaces.leftLess),
                  child: XBImage(
                    images.icon_download,
                    width: 20,
                  ),
                ),
              ),
            ),
            XBButton(
              onTap: vm.changeOrientation,
              child: Container(
                color: Colors.transparent,
                child: Padding(
                  padding: EdgeInsets.only(
                      left: spaces.leftLess,
                      right: spaces.left,
                      top: 15,
                      bottom: 15),
                  child: XBImage(
                    images.ic_live_func_fullscreen,
                    width: 20,
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
