import 'package:bcloud/api/device_tree_api.dart';
import 'package:bcloud/generated/assets.gen.dart';
import 'package:bcloud/utils/xb_direction_util/xb_direction_controller.dart';
import 'package:bcloud/widget/xb_jf_player/xb_media_player_direction.dart';
import 'package:bcloud/widget/xb_progress/xb_progress_line.dart';
import 'package:bcloud/pages/message/message_detail_page_vm.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/app_extension.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/xb_page_over.dart';
import 'package:bcloud/widget/xb_dismiss_tool_bar_widget/xb_dismiss_tool_bar_widget.dart';
import 'package:bcloud/widget/xb_img_area_mask_widget/xb_img_area_mask_widget.dart';
import 'package:bcloud/widget/xb_jf_player/xb_player_cover.dart';
import 'package:card_swiper/card_swiper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xb_scaffold/xb_scaffold.dart';
import 'message_detail_item_list_view_page.dart';
import 'message_detail_item_text_view_page.dart';

class MessageDetailPage extends XBPageOver<MessageDetailPageVM> {
  final int type;

  ///1 消息列表进入传入model 2 设备直播页面进入传入deviceId 3 通过消息通知进入传入messageId
  final String? deviceId;
  final String? messageId;
  final String? alarmTime;
  final MessageAIModel? model;
  const MessageDetailPage(
      {required this.type,
      this.deviceId,
      this.messageId,
      this.alarmTime,
      this.model,
      super.key});

  @override
  generateVM(BuildContext context) {
    return MessageDetailPageVM(
        context: context,
        type: type,
        deviceId: deviceId,
        messageId: messageId,
        alarmTime: alarmTime);
  }

  @override
  bool needRebuildWhileOrientationChanged(MessageDetailPageVM vm) {
    return true;
  }

  @override
  bool needShowContentFromScreenTop(MessageDetailPageVM vm) {
    return vm.isOrientationH ? true : false;
  }

  @override
  String setTitle(MessageDetailPageVM vm) {
    return vm.title;
  }

  @override
  List<Widget>? actions(MessageDetailPageVM vm) {
    return vm.isDelete
        ? [
            XBButton(
              onTap: () {
                vm.onDelete();
              },
              child: Padding(
                padding: EdgeInsets.only(right: 16.w),
                child: Assets.images.icCommonDeleteAll.svg(
                    width: 24,
                    height: 24,
                    colorFilter:
                        const ColorFilter.mode(Colors.black, BlendMode.srcIn)),
              ),
            ),
          ]
        : null;
  }

  @override
  bool needLoading(MessageDetailPageVM vm) {
    return true;
  }

  @override
  bool needIosGestureBack(MessageDetailPageVM vm) {
    if (vm.isOrientationH) {
      return false;
    } else {
      return true;
    }
  }

  @override
  bool onAndroidPhysicalBack(MessageDetailPageVM vm) {
    if (vm.isOrientationH) {
      vm.changeOrientation();
      return false;
    } else {
      return true;
    }
  }

  @override
  Widget buildPage(MessageDetailPageVM vm, BuildContext context) {
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 500),
      transitionBuilder: (Widget child, Animation<double> animation) {
        return FadeTransition(
          opacity: animation,
          child: SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(0.0, 0.1),
              end: Offset.zero,
            ).animate(CurvedAnimation(
              parent: animation,
              curve: Curves.easeOutCubic,
            )),
            child: child,
          ),
        );
      },
      child: vm.isOrientationH ? _orientationH(vm) : _orientationV(vm),
    );
  }

  _toolBarImageV(MessageDetailPageVM vm, [double? height, Color? bgColor]) {
    height ??= 60;
    bgColor ??= colors.black.withAlpha(120);
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.transparent,
            Colors.black.withOpacity(0.8),
            Colors.black.withOpacity(0.95),
          ],
          stops: const [0.0, 0.6, 1.0],
        ),
      ),
      child: Container(
        height: height,
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
        child: Row(
          children: [
            Visibility(
              visible: vm.isHasVideo,
              child: _buildToolBarButton(
                onTap: vm.onTapPlayImg,
                icon: images.ic_live_func_play,
                size: 24,
                backgroundColor: Colors.white.withOpacity(0.15),
                iconColor: Colors.white,
              ),
            ),
            const Spacer(),
            _buildToolBarButton(
              onTap: vm.onTapDownload,
              icon: images.icon_download,
              size: 22,
              backgroundColor: const Color(0xFF4CAF50).withOpacity(0.2),
              iconColor: const Color(0xFF4CAF50),
            ),
            SizedBox(width: 12.w),
            _buildToolBarButton(
              onTap: vm.changeOrientation,
              icon: images.ic_live_func_fullscreen,
              size: 22,
              backgroundColor: const Color(0xFF2196F3).withOpacity(0.2),
              iconColor: const Color(0xFF2196F3),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildToolBarButton({
    required VoidCallback onTap,
    required String icon,
    required double size,
    Color? backgroundColor,
    Color? iconColor,
  }) {
    return XBButton(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: EdgeInsets.all(8.w),
        decoration: BoxDecoration(
          color: backgroundColor ?? Colors.white.withOpacity(0.15),
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: Colors.white.withOpacity(0.2),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ColorFiltered(
          colorFilter: ColorFilter.mode(
            iconColor ?? Colors.white,
            BlendMode.srcIn,
          ),
          child: XBImage(
            icon,
            width: size,
            height: size,
          ),
        ),
      ),
    );
  }

  _toolBarVideoV(MessageDetailPageVM vm,
      [double? height, Color? bgColor, bool showImg = true]) {
    height ??= 70;
    bgColor ??= colors.black.withAlpha(120);
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.transparent,
            Colors.black.withAlpha(200),
            Colors.black.withAlpha(240),
          ],
          stops: const [0.0, 0.5, 1.0],
        ),
      ),
      child: Container(
        height: height,
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
        child: Column(
          children: [
            // 进度条区域
            Container(
              padding: EdgeInsets.symmetric(horizontal: 4.w),
              child: Row(
                children: [
                  Text(
                    vm.startTimeShow,
                    style: TextStyle(
                      color: Colors.white.withAlpha(200),
                      fontSize: 11.sp,
                      fontWeight: FontWeight.w500,
                      shadows: [
                        Shadow(
                          color: Colors.black.withAlpha(150),
                          blurRadius: 2,
                        ),
                      ],
                    ),
                  ),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: Container(
                      height: 4.h,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(2.r),
                        color: Colors.white.withAlpha(60),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(2.r),
                        child: XBProgressLine(
                          progress: vm.progress,
                          strokeWidth: 4,
                          backgroundColor: Colors.transparent,
                          foregroundColor: const Color(0xFF00BCD4),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: 8.w),
                  Text(
                    vm.endTimeShow,
                    style: TextStyle(
                      color: Colors.white.withAlpha(200),
                      fontSize: 11.sp,
                      fontWeight: FontWeight.w500,
                      shadows: [
                        Shadow(
                          color: Colors.black.withAlpha(150),
                          blurRadius: 2,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 8.h),
            // 控制按钮区域
            Expanded(
              child: Row(
                children: [
                  _buildVideoControlButton(
                    onTap: vm.onTapPlay,
                    icon: vm.isPlaying
                        ? images.ic_live_func_pause
                        : images.ic_live_func_play,
                    size: 26,
                    backgroundColor: const Color(0xFFFF5722).withAlpha(50),
                    iconColor: const Color(0xFFFF5722),
                    isMainAction: true,
                  ),
                  const Spacer(),
                  if (showImg) ...[
                    _buildVideoControlButton(
                      onTap: vm.onTapSwitchImg,
                      icon: images.icon_switch_img,
                      size: 22,
                      backgroundColor: const Color(0xFF9C27B0).withAlpha(50),
                      iconColor: const Color(0xFF9C27B0),
                    ),
                    SizedBox(width: 12.w),
                  ],
                  _buildVideoControlButton(
                    onTap: vm.changeOrientation,
                    icon: images.ic_live_func_fullscreen,
                    size: 22,
                    backgroundColor: const Color(0xFF2196F3).withAlpha(50),
                    iconColor: const Color(0xFF2196F3),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVideoControlButton({
    required VoidCallback onTap,
    required String icon,
    required double size,
    Color? backgroundColor,
    Color? iconColor,
    bool isMainAction = false,
  }) {
    return XBButton(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: EdgeInsets.all(isMainAction ? 10.w : 8.w),
        decoration: BoxDecoration(
          color: backgroundColor ?? Colors.white.withAlpha(40),
          borderRadius: BorderRadius.circular(isMainAction ? 16.r : 12.r),
          border: Border.all(
            color: (iconColor ?? Colors.white).withAlpha(80),
            width: 1.5,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(100),
              blurRadius: isMainAction ? 12 : 8,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: ColorFiltered(
          colorFilter: ColorFilter.mode(
            iconColor ?? Colors.white,
            BlendMode.srcIn,
          ),
          child: XBImage(
            icon,
            width: size,
            height: size,
          ),
        ),
      ),
    );
  }

  _orientationV(MessageDetailPageVM vm) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            const Color(0xFFF8F9FA),
            const Color(0xFFE9ECEF),
          ],
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 媒体播放区域
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(20.r),
                bottomRight: Radius.circular(20.r),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(30),
                  blurRadius: 15,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(20.r),
                bottomRight: Radius.circular(20.r),
              ),
              child: vm.isShowVideo
                  ? XBDismissToolBarWidget(
                      duration: 100000,
                      bottomBar: _toolBarVideoV(vm),
                      child: _video(vm))
                  : XBDismissToolBarWidget(
                      duration: 50000,
                      bottomBar: _toolBarImageV(vm),
                      child: _image(vm)),
            ),
          ),
          // 顶部标签栏
          topBar(vm),
          // 内容区域
          Expanded(
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 8.w),
              child: PageView(
                allowImplicitScrolling: true,
                controller: vm.pageController,
                children: [
                  MessageDetailItemTextViewPage(vm),
                  MessageDetailItemListViewPage(vm)
                ],
                onPageChanged: (value) {
                  vm.onChangeShowState(value);
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget topBar(MessageDetailPageVM vm) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(15),
            blurRadius: 20,
            offset: const Offset(0, 4),
            spreadRadius: 2,
          ),
        ],
        border: Border.all(
          color: Colors.grey.withAlpha(30),
          width: 1,
        ),
      ),
      child: Container(
        height: 56.h,
        padding: EdgeInsets.all(4.w),
        child: Row(
          children: List.generate(vm.titleItems.length, (index) {
            final isSelected = vm.selectedIndex == index;
            return Expanded(
              child: GestureDetector(
                onTap: () => vm.onChangeShowState(index),
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeInOut,
                  decoration: BoxDecoration(
                    color: isSelected
                        ? const Color(0xFF2196F3)
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(12.r),
                    boxShadow: isSelected
                        ? [
                            BoxShadow(
                              color: const Color(0xFF2196F3).withAlpha(60),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ]
                        : null,
                  ),
                  child: Center(
                    child: AnimatedDefaultTextStyle(
                      duration: const Duration(milliseconds: 300),
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight:
                            isSelected ? FontWeight.w600 : FontWeight.w500,
                        color:
                            isSelected ? Colors.white : const Color(0xFF666666),
                      ),
                      child: Text(vm.titleItems[index].title),
                    ),
                  ),
                ),
              ),
            );
          }),
        ),
      ),
    );
  }

  Widget _image(MessageDetailPageVM vm) {
    return AspectRatio(
      aspectRatio: vm.whScale,
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              const Color(0xFF1A1A1A),
              const Color(0xFF2D2D2D),
              const Color(0xFF1A1A1A),
            ],
            stops: const [0.0, 0.5, 1.0],
          ),
        ),
        child: Swiper(
          onIndexChanged: vm.onImgIndexChanged,
          loop: vm.itemCount > 1,
          autoplay: vm.itemCount > 1,
          autoplayDelay: 4000,
          duration: 800,
          curve: Curves.easeInOutCubic,
          itemBuilder: (BuildContext context, int index) {
            return Container(
              decoration: BoxDecoration(
                gradient: RadialGradient(
                  center: Alignment.center,
                  radius: 1.2,
                  colors: [
                    const Color(0xFF2A2A2A),
                    const Color(0xFF1A1A1A),
                  ],
                ),
              ),
              alignment: Alignment.center,
              child: vm.isExist(index)
                  ? Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8.r),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withAlpha(100),
                            blurRadius: 20,
                            offset: const Offset(0, 8),
                          ),
                        ],
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8.r),
                        child: XBImgAreaMaskWidget(
                          key: ValueKey(vm.areaKey),
                          image: vm.detailData(index).image,
                          boxs: vm.detailData(index).objects ?? [],
                          roiPoints: vm.roiPoints(index),
                          displayHeight: screenH,
                          displayWidth: screenW,
                          lineWidth: vm.isOrientationH ? 5 : 3,
                          onGotImgSize: vm.onGotImgSize,
                          isDrawText: true,
                          whScale: vm.whScale,
                        ),
                      ),
                    )
                  : Container(
                      padding: EdgeInsets.all(40.w),
                      decoration: BoxDecoration(
                        color: Colors.white.withAlpha(10),
                        borderRadius: BorderRadius.circular(16.r),
                        border: Border.all(
                          color: Colors.white.withAlpha(30),
                          width: 2,
                        ),
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          XBImage(
                            images.icon_no_image,
                            width: 80.w,
                            height: 80.w,
                          ),
                          SizedBox(height: 16.h),
                          Text(
                            '暂无图片',
                            style: TextStyle(
                              color: Colors.white.withAlpha(150),
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
            );
          },
          itemCount: vm.itemCount,
          pagination: vm.itemCount > 1
              ? SwiperPagination(
                  margin: EdgeInsets.only(bottom: 40.h),
                  builder: DotSwiperPaginationBuilder(
                    color: Colors.white.withAlpha(100),
                    activeColor: const Color(0xFF00BCD4),
                    size: 8.0,
                    activeSize: 12.0,
                    space: 6.0,
                  ),
                )
              : null,
        ),
      ),
    );
  }

  Widget _video(MessageDetailPageVM vm) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 400),
      curve: Curves.easeInOutCubic,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF0D1117),
            const Color(0xFF1A1A1A),
            const Color(0xFF0D1117),
          ],
          stops: const [0.0, 0.5, 1.0],
        ),
        borderRadius: BorderRadius.circular(vm.isOrientationH ? 0 : 12.r),
      ),
      width: screenW,
      height:
          XBDirectionController.isDirectionV ? screenW / vm.whScale : screenH,
      alignment: Alignment.center,
      child: vm.controllerRecord == null
          ? Container(
              decoration: BoxDecoration(
                color: Colors.white.withAlpha(10),
                borderRadius: BorderRadius.circular(16.r),
                border: Border.all(
                  color: Colors.white.withAlpha(30),
                  width: 2,
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    padding: EdgeInsets.all(20.w),
                    decoration: BoxDecoration(
                      color: const Color(0xFF2196F3).withAlpha(30),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.play_arrow_rounded,
                      size: 48.w,
                      color: const Color(0xFF2196F3),
                    ),
                  ),
                  SizedBox(height: 16.h),
                  Text(
                    '正在加载视频...',
                    style: TextStyle(
                      color: Colors.white.withAlpha(150),
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            )
          : Container(
              decoration: BoxDecoration(
                borderRadius:
                    BorderRadius.circular(vm.isOrientationH ? 0 : 8.r),
                boxShadow: vm.isOrientationH
                    ? null
                    : [
                        BoxShadow(
                          color: Colors.black.withAlpha(100),
                          blurRadius: 20,
                          offset: const Offset(0, 8),
                        ),
                      ],
              ),
              child: ClipRRect(
                borderRadius:
                    BorderRadius.circular(vm.isOrientationH ? 0 : 8.r),
                child: XBPlayerCover(
                  key: vm.playerKey,
                  controller: vm.controllerRecord!,
                  player: XbMediaPlayerDirection(
                    displaySize: Size(
                        screenW,
                        XBDirectionController.isDirectionV
                            ? screenW / vm.whScale
                            : screenH),
                    isFill: XBDirectionController.isDirectionV,
                    whScale: vm.whScale,
                    controller: vm.controllerRecord!.mediaController,
                  ),
                  errorBuilder: (context) {
                    return _buildVideoErrorWidget(vm);
                  },
                  retryFailureBuilder: (context) {
                    return _buildVideoErrorWidget(vm);
                  },
                ),
              ),
            ),
    );
  }

  Widget _buildVideoErrorWidget(MessageDetailPageVM vm) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.black.withAlpha(200),
        borderRadius: BorderRadius.circular(vm.isOrientationH ? 0 : 8.r),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: EdgeInsets.all(20.w),
            decoration: BoxDecoration(
              color: const Color(0xFFFF5722).withAlpha(30),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.error_outline_rounded,
              size: 48.w,
              color: const Color(0xFFFF5722),
            ),
          ),
          SizedBox(height: 16.h),
          Text(
            '视频加载失败',
            style: TextStyle(
              color: Colors.white.withAlpha(200),
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 8.h),
          GestureDetector(
            onTap: vm.retry,
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 40.w, vertical: 12.h),
              padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
              decoration: BoxDecoration(
                color: const Color(0xFFFF5722),
                borderRadius: BorderRadius.circular(25.r),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFFFF5722).withAlpha(60),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.refresh_rounded,
                    color: Colors.white,
                    size: 20.w,
                  ),
                  SizedBox(width: 8.w),
                  Text(
                    '重试',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  _orientationH(MessageDetailPageVM vm) {
    return Container(
      alignment: Alignment.center,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF0A0A0A),
            const Color(0xFF1A1A1A),
            const Color(0xFF0A0A0A),
          ],
          stops: const [0.0, 0.5, 1.0],
        ),
      ),
      child: XBDismissToolBarWidget(
        topBar: Container(
          padding: EdgeInsets.only(left: 16.w, top: stateBarH + 8.h),
          child: SizedBox(
            height: naviBarH,
            child: Row(
              children: [
                _buildHorizontalBackButton(vm),
                const Spacer(),
                // 可以在这里添加其他横屏顶部按钮
              ],
            ),
          ),
        ),
        bottomBar: vm.isShowVideo ? _toolBarVideoH(vm) : _toolBarH(vm),
        child: vm.isShowVideo ? _video(vm) : _image(vm),
      ),
    );
  }

  Widget _buildHorizontalBackButton(MessageDetailPageVM vm) {
    return XBButton(
      onTap: () {
        vm.back();
      },
      child: Container(
        padding: EdgeInsets.all(12.w),
        decoration: BoxDecoration(
          color: Colors.black.withAlpha(100),
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: Colors.white.withAlpha(40),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(150),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: ColorFiltered(
          colorFilter: const ColorFilter.mode(
            Colors.white,
            BlendMode.srcIn,
          ),
          child: XBImage(
            images.ic_common_back_white,
            width: 24.w,
            height: 24.w,
          ),
        ),
      ),
    );
  }

  Widget _toolBarVideoH(MessageDetailPageVM vm) {
    double height = 70;
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.transparent,
            Colors.black.withAlpha(200),
            Colors.black.withAlpha(240),
          ],
          stops: const [0.0, 0.5, 1.0],
        ),
      ),
      child: _toolBarVideoV(vm, height, Colors.transparent, false),
    );
  }

  Widget _toolBarH(MessageDetailPageVM vm) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.transparent,
            Colors.black.withAlpha(200),
            Colors.black.withAlpha(240),
          ],
          stops: const [0.0, 0.5, 1.0],
        ),
      ),
      child: Container(
        height: 60.h,
        padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 8.h),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            _buildHorizontalToolButton(
              onTap: vm.onTapDownload,
              icon: images.icon_download,
              backgroundColor: const Color(0xFF4CAF50).withAlpha(50),
              iconColor: const Color(0xFF4CAF50),
            ),
            SizedBox(width: 16.w),
            _buildHorizontalToolButton(
              onTap: vm.changeOrientation,
              icon: images.ic_live_func_fullscreen,
              backgroundColor: const Color(0xFF2196F3).withAlpha(50),
              iconColor: const Color(0xFF2196F3),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHorizontalToolButton({
    required VoidCallback onTap,
    required String icon,
    Color? backgroundColor,
    Color? iconColor,
  }) {
    return XBButton(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(12.w),
        decoration: BoxDecoration(
          color: backgroundColor ?? Colors.white.withAlpha(40),
          borderRadius: BorderRadius.circular(14.r),
          border: Border.all(
            color: (iconColor ?? Colors.white).withAlpha(80),
            width: 1.5,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(120),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: ColorFiltered(
          colorFilter: ColorFilter.mode(
            iconColor ?? Colors.white,
            BlendMode.srcIn,
          ),
          child: XBImage(
            icon,
            width: 24.w,
            height: 24.w,
          ),
        ),
      ),
    );
  }
}
