import 'dart:async';
import 'dart:io';

import 'package:bcloud/api/bc_api.dart';
import 'package:bcloud/api/core/dio_config.dart';
import 'package:bcloud/api/device_tree_api.dart';
import 'package:bcloud/config/authority_check_config.dart';
import 'package:bcloud/config/common_path.dart';
import 'package:bcloud/config/constant.dart';
import 'package:bcloud/config/events.dart';
import 'package:bcloud/generated/l10n.dart';
import 'package:bcloud/pages/device_attribute/device_attribute_page.dart';
import 'package:bcloud/pages/device_tree/device_syncIntercept_detail_page.dart';
import 'package:bcloud/pages/device_tree/edit_node_info_page.dart';
import 'package:bcloud/pages/device_tree/edit_node_page.dart';
import 'package:bcloud/pages/device_tree/model/device_tree.dart';
import 'package:bcloud/pages/device_tree/model/item_operation_model.dart';
import 'package:bcloud/pages/login/model/account_info.dart';
import 'package:bcloud/pages/preview/device_info_page.dart';
import 'package:bcloud/pages/preview/device_setting_page.dart';
import 'package:bcloud/pages/preview/model/device.dart';
import 'package:bcloud/pages/preview/preview_page.dart';
import 'package:bcloud/public/network/net_extension_device_tree.dart';
import 'package:bcloud/public/network/net_query_util.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/app_extension.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/xb_scaffold_extension.dart';
import 'package:bcloud/utils/opera_log_util/opera_log_util.dart';
import 'package:bcloud/utils/xb_video_stream_manager/xb_live_stream_manager.dart';
import 'package:bcloud/widget/x_dialog.dart';
import 'package:bcloud/widget/x_text_field.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:xb_refresh/xb_refresh.dart';
import 'package:xb_scaffold/xb_scaffold.dart';

class DeviceOrganizationController extends ChangeNotifier {
  static int retryTime = 0;
  static bool canChooseNode = true;
  final BuildContext context;
  // final RefreshController refreshController = RefreshController(initialRefresh: false);
  final XBRefreshController refreshController = XBRefreshController();
  final ScrollController chooseScrollController = ScrollController();
  final List<ItemOperationModel> modelList = [];
  final Map<int, String> commonFavoriteMap = {
    0: images.icon_collect_unselect,
    1: images.icon_collect_part_select,
    2: images.icon_collect_selected,
  };
  final Map<int, String> expandDeviceFavoriteMap = {
    0: images.icon_collect_unselect_white,
    2: images.icon_collect_selected_white,
  };

  ///上方节点列表
  List<DeviceTreeNode> tabNodes = [];
  DeviceTreeNode? currentDeviceNode;
  DeviceTreeNode? currentNode;
  //是否展开双列
  bool expand = true;
  int get expandMode => expand ? 0 : 1;

  late StreamSubscription _changeNameSubscription;
  late StreamSubscription _deleteSubscription;
  late StreamSubscription _refreshHomePageSubscription;
  late StreamSubscription _languageSubscribe;
  late StreamSubscription favoriteDeviceSubscribe;

  DeviceOrganizationController({required this.context}) {
    _changeNameSubscription =
        eventBus.on<ChangeDeviceNameEvent>().listen((event) {
      refresh();
    });
    _deleteSubscription = eventBus.on<DeleteDeviceEvent>().listen((event) {
      refresh();
    });
    _refreshHomePageSubscription =
        eventBus.on<EventRefreshHomePage>().listen((event) {
      refresh();
    });
    _languageSubscribe =
        eventBus.on<EventRefreshLanguageSetting>().listen((event) {
      tabNodes.clear();
      initTreeNode();
    });
    favoriteDeviceSubscribe =
        eventBus.on<EventRefreshFavoriteDevice>().listen((event) {
      refresh();
    });
  }

  changeExpand(bool isExpand) {
    if (isExpand == expand) {
      return;
    }
    if (isHideDevice) {
      toast(isExpand
          ? TR.current.tr_chooseExpandGridMode
          : TR.current.tr_chooseExpandListMode);
    }
    expand = isExpand;
    notifyListeners();
  }

  String syncText(int? type) {
    if (type == null) {
      return "";
    } else if (type == 0) {
      return TR.current.tr_insufficientAuthorization;
    } else if (type == 2) {
      return TR.current.tr_insufficientChannels;
    } else {
      return "";
    }
  }

  DeviceTreeNode? get deviceNode => currentDeviceNode;

  DeviceTreeNode? get parentNode => tabNodes.isNotEmpty ? tabNodes.last : null;

  bool get isSpecialTreeNode => (parentNode?.checkType ?? 0) != 1;

  ItemOperationModel get channelsOperationModel => ItemOperationModel(
      type: '1',
      name: TR.current.tr_channelNum,
      image: images.ic_organization_operate_channels);

  ItemOperationModel get syncOperationModel => ItemOperationModel(
      type: '2',
      name: TR.current.tr_exeSync,
      image: images.ic_organization_operate_sync);

  ItemOperationModel get detailOperationModel => ItemOperationModel(
      type: '3',
      name: TR.current.tr_detail,
      image: images.ic_organization_operate_detail);

  ItemOperationModel get deleteOperationModel => ItemOperationModel(
      type: '4',
      name: TR.current.tr_Common_Delete,
      image: images.ic_organization_operate_delete);

  ItemOperationModel get editOperationModel => ItemOperationModel(
      type: '5',
      name: TR.current.tr_Common_Rename,
      image: images.ic_organization_operate_edit);

  ItemOperationModel get settingOperationModel => ItemOperationModel(
      type: '6',
      name: TR.current.tr_NormalSetting,
      image: images.ic_organization_operate_setting);

  ItemOperationModel get tagOperationModel => ItemOperationModel(
      type: '7',
      name: TR.current.tr_bindTag,
      image: images.ic_organization_operate_bind_tag);

  ItemOperationModel get nodeDetailOperationModel => ItemOperationModel(
      type: '8',
      name: TR.current.tr_detail,
      image: images.ic_organization_operate_detail);

  List<TreeNode> get currentNodeChildren {
    List<TreeNode> nodes = [];
    if (currentNode != null &&
        currentNode!.children != null &&
        currentNode!.children!.isNotEmpty) {
      nodes.addAll(currentNode!.children!);
    }
    if (currentNode != null &&
        currentNode!.devices != null &&
        currentNode!.devices!.isNotEmpty) {
      nodes.addAll(currentNode!.devices!);
    }
    return nodes;
  }

  List<TreeNode> get currentNodeList {
    List<TreeNode> nodes = [];
    if (currentNode != null &&
        currentNode!.children != null &&
        currentNode!.children!.isNotEmpty) {
      nodes.addAll(currentNode!.children!);
    }
    return nodes;
  }

  List<DeviceTreeDevice> get currentDeviceList {
    List<DeviceTreeDevice> devices = [];
    if (currentNode != null &&
        currentNode!.devices != null &&
        currentNode!.devices!.isNotEmpty) {
      devices.addAll(currentNode!.devices!);
    }
    return devices;
  }

  int get currentNodeCount => currentNodeList.length;

  int get currentDeviceCount => currentDeviceList.length;

  bool get currentNodeNotEmpty => currentNodeList.isNotEmpty;

  bool get isHasDevice => currentDeviceList.isNotEmpty;

  bool get isHideDevice => currentDeviceList.isEmpty;

  initTreeNode() async {
    if (!AccountInfo.instance.isLogin) {
      return;
    }
    queryNodeInfoAddDevice(id: 0, root: true);
    int rootId = await getCurrentNode(notify: false, root: true);
    if (rootId >= 0) {
      //请求根节点信息
      await getCurrentNode(id: rootId);
    } else {
      notifyListeners();
    }
  }

  queryNodeInfoAddDevice({required int id, bool root = false}) {
    BCApi.bcDevice.getDeviceTreeNodeAddDevice(id).then((value) {
      if (value != null && value.isNotEmpty) {
        currentDeviceNode = value.first;
      }
    }).catchError((e) {
      // toastFailure(kErrorMsg(e));
    });
  }

  Future<int> getCurrentNode({
    int id = 0,
    bool notify = true,
    bool root = false,
  }) async {
    if (!AccountInfo.instance.isLogin) {
      return -1;
    }
    try {
      // List<DeviceTreeNode> nodes =
      //     await deviceTreeAPI.getDeviceTreeNode(id, showDevice: true);
      List<DeviceTreeNode> nodes = await NetQueryUtil.getInstance()
          .getDeviceTreeNode(id, showDevice: true);
      DeviceTreeNode? node;
      if (nodes.isNotEmpty) {
        node = nodes.first;
      }

      if (root && node != null) {
        //添加 root node 到 tab 上
        if (!tabNodes.contains(node)) {
          node.isVirtualNode = node.id == 0;
          tabNodes.add(node);
        }
      }
      if (node?.devices?.isNotEmpty ?? false) {
        for (var device in node!.devices!) {
          String thumbnailPath =
              await kGenPreviewMediaFilePath(deviceId: device.id.toString());
          if (File(thumbnailPath).existsSync()) {
            device.thumbnail = thumbnailPath;
          }
        }
      }
      currentNode = node;
      // if (isHideDevice && !root) {
      //   expand = true;
      // }
      if (notify) {
        refreshController.endRefresh();
        // refreshController.refreshCompleted();
        notifyListeners();
      }
      if (node != null) {
        return currentNode!.id;
      }
    } catch (e) {
      if (notify) {
        refreshController.endRefresh();
        // refreshController.refreshFailed();
      }
    }
    return 0;
  }

  refreshLocalNode(TreeNode node) async {
    if (!AccountInfo.instance.isLogin) {
      return;
    }
    await getCurrentNode(id: node.id);
  }

  chooseNode(TreeNode node, {bool fromTitle = false}) async {
    if (node.nodeType == nodeTypeDevice) {
      if (!canChooseNode) {
        return;
      }
      canChooseNode = false;
      showLoadingGlobal(contentEnable: true);
      if (node is DeviceTreeDevice && !node.isOnline) {
        Device device = Device(
            uuid: node.id.toString(),
            url: "",
            deviceName: node.name,
            deviceType: node.deviceType,
            favoriteStatus: node.favoriteStatus,
            isMultiLevel: node.isMultiLevel);
        push(PreviewPage(
          device: device,
          isDisableInit: true,
          disableTipInit: TR.current.tr_deviceOffline,
        )).then((value) => refresh());
        canChooseNode = true;
        hideLoadingGlobal();
        return;
      }
      String id = '${node.id}';
      callback(value) {
        if (stackContainType(PreviewPage)) {
          return;
        }
        final url = value[id]?.rtspPriUrl;
        final deviceType = value[id]?.deviceType;
        if (url != null && url.isNotEmpty) {
          Device device = Device(
              uuid: node.id.toString(),
              url: url,
              deviceName: node.name,
              deviceType: deviceType,
              favoriteStatus: node.favoriteStatus,
              isMultiLevel: node.isMultiLevel);
          push(PreviewPage(device: device)).then((value) => refresh());
          canChooseNode = true;
          hideLoadingGlobal();
          retryTime = 0;
        } else {
          if ((value[id]?.notice != null && value[id]?.notice!.isNotEmpty) ||
              retryTime >= 3) {
            final deviceType = value[id]?.deviceType;
            Device device = Device(
                uuid: node.id.toString(),
                url: url,
                deviceName: node.name,
                deviceType: deviceType,
                favoriteStatus: node.favoriteStatus,
                isMultiLevel: node.isMultiLevel);
            push(PreviewPage(
                    device: device,
                    isDisableInit: true,
                    disableTipInit:
                        value[id]?.notice ?? TR.current.tr_deviceOffline))
                .then((value) => refresh());
            canChooseNode = true;
            hideLoadingGlobal();
            retryTime = 0;
          } else {
            retryTime++;
            Future.delayed(const Duration(milliseconds: 2000), () {
              XBLiveStreamManager()
                  .getVideoStreamForDeviceIds(ids: [id], onDone: callback);
            });
          }
        }
      }

      XBLiveStreamManager()
          .getVideoStreamForDeviceIds(ids: [id], onDone: callback);
      return;
    }
    if (fromTitle) {
      int index = tabNodes.indexOf(node as DeviceTreeNode);
      tabNodes.removeRange(index + 1, tabNodes.length);
    } else {
      bool isNotHas =
          tabNodes.where((element) => node.id == element.id).toList().isEmpty;
      if (node is DeviceTreeNode && isNotHas) {
        tabNodes.add(node);
      }
    }
    notifyListeners();
    await getCurrentNode(id: node.id);
    if (tabNodes.length > 1) {
      Future.delayed(const Duration(milliseconds: 500), () {
        chooseScrollController.animateTo(
            chooseScrollController.position.maxScrollExtent,
            duration: const Duration(milliseconds: 100),
            curve: Curves.bounceIn);
      });
    }
  }

  requestInitTreeNode() {
    tabNodes.clear();
    initTreeNode();
  }

  refresh() {
    if (tabNodes.isNotEmpty) {
      refreshLocalNode(tabNodes.last);
    } else {
      initTreeNode();
    }
  }

  bool isHideMoreViewOperation(TreeNode node) {
    return (node.checkType == 1 ||
            node.checkType == 10 ||
            node.checkType == 12) &&
        (!AuthorityCheckConfig().isHasConfigDeviceOptRemove) &&
        (!AuthorityCheckConfig().isHasConfigDeviceOptModify);
  }

  ///删除节点
  deleteNode(
    VoidCallback callback,
    TreeNode node,
  ) async {
    if (node.nodeType == nodeTypeDevice) {
      if (!AuthorityCheckConfig().isHasConfigDeviceOptRemove) {
        toastFailure(TR.current.tr_hasNoAuthorization);
        return;
      }
      String? password = await _checkPassword(callback, node.id);
      if (password != null && password.isEmpty) {
        return;
      }
      await dialog(
          title: TR.current.tr_gentleReminder,
          msg: TR.current.tr_MakeSureDeleteDevice,
          btnTitles: [TR.current.tr_Cancel, TR.current.tr_Confirm],
          btnHighLightColor: colors.blue,
          onSelected: (dlIndex) async {
            if (dlIndex == 1) {
              deviceTreeAPI
                  .deleteDevice('${node.id}', password: password)
                  .then((value) {
                callback.call();
                OperaLogUtil.videoMonitor
                    .logDeleteDevice(deviceName: node.name);
                toastSuccess(TR.current.tr_Common_DeleteSuccess);
                eventBus.fire(DeleteDeviceEvent());
              });
            }
          });
      // //删除设备
      // showDialog(
      //     context: context,
      //     builder: (context) {
      //       return XDialog(
      //         subTitle: TR.current.tr_MakeSureDeleteDevice,
      //         confirmTextStyle: TextStyle(
      //             fontSize: 16.sp,
      //             fontWeight: FontWeight.bold,
      //             color: const Color(0xFFE44C4C)),
      //         onConfirm: () async {
      //           callback.call();
      //           await deviceTreeAPI.deleteDevice('${node.id}',
      //               password: password);
      //           toastSuccess(TR.current.tr_Common_DeleteSuccess);
      //           eventBus.fire(DeleteDeviceEvent());
      //         },
      //       );
      //     });
    } else {
      if (!AuthorityCheckConfig().isHasConfigDeviceTree) {
        toastFailure(TR.current.tr_hasNoAuthorization);
        return;
      }
      if (node.accessType == 1) {
        String? password = await _checkPassword(callback, node.id);
        if (password != null && password.isEmpty) {
          return;
        }
        await dialog(
            title: TR.current.tr_Common_Delete,
            msg: TR.current.tr_MoveDevicesToRootNVRNode,
            btnTitles: [TR.current.tr_Cancel, TR.current.tr_Confirm],
            btnHighLightColor: colors.blue,
            onSelected: (dlIndex) async {
              if (dlIndex == 1) {
                _deleteNode(callback, node.id, password: password);
              }
            });
      } else if (node.accessType == 3) {
        String? password = await _checkPassword(callback, node.id);
        if (password != null && password.isEmpty) {
          return;
        }
        await dialog(
            title: TR.current.tr_Common_Delete,
            msg: TR.current.tr_MoveDevicesToCenterSeverNode,
            btnTitles: [TR.current.tr_Cancel, TR.current.tr_Confirm],
            btnHighLightColor: colors.blue,
            onSelected: (dlIndex) async {
              if (dlIndex == 1) {
                _deleteNode(callback, node.id, password: password);
              }
            });
      } else {
        bool isStoreNode = await checkNodeIsStore('${node.id}');
        if (isStoreNode) {
          dialog(
              title: TR.current.tr_pleaseUnbindStoreId,
              msg: node.name,
              btnTitles: [TR.current.tr_Confirm],
              btnHighLightColor: colors.blue,
              onSelected: (dlIndex) {
                // pop(context);
              });
          return;
        }
        bool isHasChildren = await checkNodeHasChildren(node.id);
        if (isHasChildren) {
          dialog(
              title: TR.current.tr_pleaseUnbindHasChildren,
              msg: node.name,
              btnTitles: [TR.current.tr_Confirm],
              btnHighLightColor: colors.blue,
              onSelected: (dlIndex) {
                // pop(context);
              });
          return;
        }
        try {
          String? password = await _checkPassword(callback, node.id);
          if (password != null && password.isEmpty) {
            return;
          }
          _checkHasDevice(callback, node, node.id, password: password);
        } catch (e) {
          debugPrint(e.toString());
        }
      }
    }
  }

  ///返回  null 是不需要校验
  ///返回 '' 是点击了取消
  ///返回 'password' 是输入了密码
  Future<String?> _checkPassword(VoidCallback callback, int id) async {
    bool pass = await deviceTreeAPI.queryIdentify(id);
    if (!pass && context.mounted) {
      TextEditingController editingController = TextEditingController();
      DialogAction action = await showDialog(
          context: context,
          builder: (context) {
            return XDialog(
              title: TR.current.tr_Prompt,
              content: Container(
                decoration: BoxDecoration(
                  border: Border.all(
                    color: const Color(0xFFDCDEE3), // 边框颜色
                    width: onePixel, // 边框宽度
                  ),
                  borderRadius: BorderRadius.circular(3.r),
                  color: Colors.white,
                ),
                child: XTextField(
                  height: 40,
                  hintText: TR.current.tr_Common_InputPassword,
                  borderRadius: BorderRadius.circular(3.r),
                  backgroundColor: Colors.white,
                  onValueChanged: (text) {},
                  controller: editingController,
                ),
              ),
              onConfirm: () {},
            );
          });
      if (action == DialogAction.confirm) {
        return editingController.text;
      }
      if (action == DialogAction.cancel) {
        return '';
      }
    }
    return null;
  }

  ///检测节点下是否有设备, 若有设备,提示 设备将会移动到跟目录
  _checkHasDevice(VoidCallback callback, TreeNode currentNode, int id,
      {String? password}) async {
    // List<DeviceTreeNode> nodes =
    //     await deviceTreeAPI.getDeviceTreeNode(id, showDevice: true);
    List<DeviceTreeNode> nodes = await NetQueryUtil.getInstance()
        .getDeviceTreeNode(id, showDevice: true);
    if (nodes.isEmpty) {
      return;
    }
    DeviceTreeNode node = nodes[0];
    List<DeviceTreeNode> children = node.children ?? [];
    List<DeviceTreeDevice> devices = node.devices ?? [];
    if (children.isNotEmpty && context.mounted) {
      showDialog(
          context: context,
          builder: (context) {
            return XDialog(
              title: TR.current.tr_Prompt,
              subTitle: TR.current.tr_OprationDeny,
              onConfirm: () {
                callback.call();
              },
            );
          });
      return;
    }

    if (devices.isNotEmpty) {
      String content = TR.current.tr_MoveDevicesToRootNode;
      if (currentNode.accessType == 1) {
        content = TR.current.tr_MoveDevicesToRootNVRNode;
      } else if (currentNode.accessType == 3) {
        content = TR.current.tr_MoveDevicesToCenterSeverNode;
      }
      if (context.mounted) {
        showDialog(
            context: context,
            builder: (context) {
              return XDialog(
                title: TR.current.tr_Common_Delete,
                subTitle: content,
                confirmTextStyle: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFFE44C4C)),
                onConfirm: () {
                  _deleteNode(callback, id, password: password);
                },
              );
            });
      }
      return;
    }
    if (context.mounted) {
      showDialog(
          context: context,
          builder: (context) {
            return XDialog(
              title: TR.current.tr_Common_Delete,
              subTitle: TR.current.tr_MakeSureDeleteNode,
              confirmTextStyle: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFFE44C4C)),
              onConfirm: () {
                _deleteNode(callback, id, password: password);
              },
            );
          });
    }
  }

  _deleteNode(VoidCallback callback, int id, {String? password}) {
    deviceTreeAPI.deleteTreeNode(id, password: password).then((value) {
      if (value) {
        toastSuccess(TR.current.tr_Common_DeleteSuccess);
        callback.call();
        refresh();
      }
    }).catchError((error) {
      toastFailure(kErrorMsg(error));
    });
  }

  ///跳转到相                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      .
  ///应的节点
  ///[levelCode] "282-283-284" 依次为父节点
  ///需跳转到 284 节点
  toNode(Map<String, dynamic> map) async {
    String levelCode = map['levelCode'];
    List<int> levels = levelCode.split('-').map((e) => int.parse(e)).toList();
    //如果根节点id为0,则说明是 子账号分配的虚拟跟节点
    //搜索出来的子节点,的 levelCode需要特殊处理, 将 255-256 改为 0-256
    if (tabNodes.first.id == 0) {
      levels[0] = 0;
    }
    levels.add(map['id']);

    List<int> currentNodeIds = tabNodes.map((e) => e.id).toList();
    List<int> moveNodeIds =
        currentNodeIds.toSet().difference(levels.toSet()).toList();
    int? parentId;
    if (moveNodeIds.isNotEmpty) {
      parentId = currentNodeIds[currentNodeIds.indexOf(moveNodeIds.first) - 1];
    }
    List<int> addNodeIds =
        levels.toSet().difference(currentNodeIds.toSet()).toList();
    if (moveNodeIds.isEmpty && addNodeIds.isEmpty) {
      return;
    }

    tabNodes.removeWhere((e) => moveNodeIds.contains(e.id));

    if (parentId != null) {
      await getCurrentNode(id: parentId);
    }

    for (var level in addNodeIds) {
      tabNodes.add((currentNode as DeviceTreeNode)
          .children!
          .firstWhere((e) => e.id == level));
      await getCurrentNode(id: level);
    }
    Future.delayed(const Duration(milliseconds: 500), () {
      chooseScrollController.animateTo(
          chooseScrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 100),
          curve: Curves.bounceIn);
    });
  }

  String getFavoriteImagePath(int value, int type) {
    if (type == 0) {
      return commonFavoriteMap[value] ?? images.icon_collect_unselect;
    }
    return expandDeviceFavoriteMap[value] ?? images.icon_collect_unselect_white;
  }

  deviceAddFavorite(int id) {
    executeMarkAddOrCancelFavorite(id, 1, 0);
  }

  deviceCancelFavorite(int id) {
    executeMarkAddOrCancelFavorite(id, 1, 1);
  }

  nodeAddFavorite(int id) {
    executeMarkAddOrCancelFavorite(id, 0, 0);
  }

  nodeCancelFavorite(int id) {
    executeMarkAddOrCancelFavorite(id, 0, 1);
  }

  executeMarkAddOrCancelFavorite(int id, int type, int addOrCancel) {
    deviceTreeAPI
        .favoriteAddOrCancel(id, type: type, addOrCancel: addOrCancel)
        .then((value) {
      refresh();
      eventBus.fire(EventRefreshFavoriteDevice());
    }).catchError((error) {
      toastFailure(kErrorMsg(error));
    });
  }

  @override
  void dispose() {
    chooseScrollController.dispose();
    _changeNameSubscription.cancel();
    _deleteSubscription.cancel();
    _refreshHomePageSubscription.cancel();
    _languageSubscribe.cancel();
    favoriteDeviceSubscribe.cancel();
    super.dispose();
  }

  void editNode(BuildContext mContext, TreeNode node) async {
    if (node.nodeType == nodeTypeDevice) {
      if (!AuthorityCheckConfig().isHasConfigDeviceOptModify) {
        toast(TR.current.tr_hasNoAuthorization);
        return;
      }
      final result = await replace(EditNodePage(
        node: node,
      ));
      if (result != null) {
        refresh();
      }
    } else {
      if (!AuthorityCheckConfig().isHasConfigDeviceTree) {
        toast(TR.current.tr_hasNoAuthorization);
        return;
      }
      final result = await replace(EditNodePage(
        node: node,
      ));
      if (result != null) {
        refresh();
      }
    }
  }

  editNodeInfo(BuildContext mContext, TreeNode node) async {
    if (!AuthorityCheckConfig().isHasConfigDeviceTree) {
      toast(TR.current.tr_hasNoAuthorization);
      return;
    }
    final result = await replace(EditNodeInfoPage(
      node: node,
    ));
    if (result != null) {
      refresh();
    }
  }

  goDeviceSetting(BuildContext mContext, TreeNode node) async {
    if (!AuthorityCheckConfig().isHasConfigDeviceOptModify &&
        !AuthorityCheckConfig().isHasConfigDeviceOptRemove) {
      toast(TR.current.tr_hasNoAuthorization);
      return;
    }
    if (node.nodeType == nodeTypeDevice) {
      await replace(DeviceSettingPage(
        deviceId: node.id.toString(),
        deviceName: node.name,
        isMultiLevel: node.isMultiLevel,
      ));
    } else {
      String deviceId = (node.deviceId?.isNotEmpty ?? false)
          ? node.deviceId!
          : node.id.toString();
      await replace(DeviceSettingPage(
          deviceId: deviceId,
          deviceName: node.name,
          isMultiLevel: node.isMultiLevel,
          nodeId: node.id,
          type: 1));
    }
  }

  goDeviceBindTag(BuildContext mContext, TreeNode node) async {
    if (node.nodeType == nodeTypeDevice) {
      if (!AuthorityCheckConfig().isHasBindTag) {
        toast(TR.current.tr_hasNoAuthorization);
        return;
      } else {
        await replace(DeviceAttributePage(
          type: 1,
          deviceId: (node.deviceId?.isNotEmpty ?? false)
              ? node.deviceId!
              : node.id.toString(),
        ));
      }
    }
  }

  checkSyncAction(BuildContext context, TreeNode node, bool isSync) async {
    showLoadingGlobal();
    String deviceId = (node.deviceId?.isNotEmpty ?? false)
        ? node.deviceId!
        : node.id.toString();
    deviceTreeAPI.getDeviceInfo(deviceId).then((value) async {
      hideLoadingGlobal();
      modelList.clear();
      int? type;
      if (value != null) {
        if (value.type == 0 || value.type == 2) {
          type = value.type;
          modelList.add(channelsOperationModel);
        }
        if (isSync) {
          if (value.accessType == 3 && value.runStatus == 1) {
            modelList.add(syncOperationModel);
          }
        }
        modelList.add(detailOperationModel);
        if (AuthorityCheckConfig().isHasConfigDeviceOptRemove) {
          modelList.add(deleteOperationModel);
        }
        if (AuthorityCheckConfig().isHasConfigDeviceOptModify) {
          modelList.add(editOperationModel);
        }
        if (!isSync && value.isJFDevice) {
          modelList.add(settingOperationModel);
        }
      } else {
        modelList.add(detailOperationModel);
        if (AuthorityCheckConfig().isHasConfigDeviceOptRemove) {
          modelList.add(deleteOperationModel);
        }
        if (AuthorityCheckConfig().isHasConfigDeviceOptModify) {
          modelList.add(editOperationModel);
        }
      }
      await showOperationDialog(context, node, modelList,
          deviceId: value.id, empowerType: type);
    }).catchError((e) {
      hideLoadingGlobal();
      toastFailure(kErrorMsg(e));
    });
  }

  exeSyncAction(BuildContext context, String deviceId) async {
    showLoadingGlobal();
    deviceTreeAPI.executeNationalPlatformSync(deviceId).then((value) async {
      hideLoadingGlobal();
      pop(context);
      refresh();
    }).catchError((e) {
      hideLoadingGlobal();
      toastFailure(kErrorMsg(e));
    });
  }

  selectOnTap(BuildContext context, TreeNode node, String type,
      {String? deviceId, int? empowerType}) async {
    switch (type) {
      case '1':
        await replace(
            DeviceSyncInterceptDetailPage(id: deviceId!, type: empowerType!));
        break;
      case '2':
        exeSyncAction(context, deviceId!);
        break;
      case '3':
        await replace(DeviceInfoPage(
          deviceId: (node.deviceId?.isNotEmpty ?? false)
              ? node.deviceId!
              : node.id.toString(),
          treeNode: node,
          isMultiLevel: node.isMultiLevel,
        ));
        break;
      case '4':
        deleteNode(() => Navigator.pop(context), node);
        break;
      case '5':
        editNode(context, node);
        break;
      case '6':
        goDeviceSetting(context, node);
        break;
      case '7':
        goDeviceBindTag(context, node);
        break;
      case '8':
        editNodeInfo(context, node);
        break;
    }
  }

  Future<dynamic> showOperationDialog(
      BuildContext context, TreeNode node, List<ItemOperationModel> modelList,
      {String? deviceId, int? empowerType}) {
    return showModalBottomSheet(
      context: xbGlobalContext,
      backgroundColor: Colors.transparent,
      isDismissible: true,
      isScrollControlled: true,
      builder: (context) {
        return StatefulBuilder(builder: (context, setState) {
          return _buildModernOperationDialog(node, modelList, context,
              deviceId: deviceId, empowerType: empowerType);
        });
      },
    );
  }

  Widget _buildModernOperationDialog(
    TreeNode node,
    List<ItemOperationModel> modelList,
    BuildContext context, {
    String? deviceId,
    int? empowerType,
  }) {
    return Container(
      width: screenW,
      constraints: BoxConstraints(
        maxHeight: screenH * 0.6,
        minHeight: 280,
      ),
      decoration: BoxDecoration(
        color: colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(25),
          topRight: Radius.circular(25),
        ),
        boxShadow: [
          BoxShadow(
            color: colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, -4),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildModernDialogHeader(node, context),
          Flexible(
            child: _buildModernOperationGrid(modelList, node, context,
                deviceId: deviceId, empowerType: empowerType),
          ),
          SizedBox(height: safeAreaBottom),
        ],
      ),
    );
  }

  Widget _buildModernDialogHeader(TreeNode node, BuildContext context) {
    return Container(
      padding: EdgeInsets.all(spaces.leftLess),
      decoration: BoxDecoration(
        color: colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(25),
          topRight: Radius.circular(25),
        ),
      ),
      child: Column(
        children: [
          // 拖拽指示器
          Container(
            width: 40,
            height: 4,
            margin: EdgeInsets.only(bottom: spaces.leftLess),
            decoration: BoxDecoration(
              color: colors.grey808080.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          // 头部信息
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(spaces.j8),
                decoration: BoxDecoration(
                  color: colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: XBImage(
                  node.imagePath,
                  width: 24,
                  height: 24,
                ),
              ),
              SizedBox(width: spaces.leftLess),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      node.name,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        fontSize: fontSizes.s16,
                        fontWeight: fontWeights.bold,
                        color: colors.black,
                      ),
                    ),
                    SizedBox(height: spaces.j4),
                    Text(
                      '选择操作',
                      style: TextStyle(
                        fontSize: fontSizes.s12,
                        color: colors.grey5B5B5B,
                      ),
                    ),
                  ],
                ),
              ),
              XBButton(
                onTap: () => Navigator.pop(context),
                child: Container(
                  padding: EdgeInsets.all(spaces.j8),
                  decoration: BoxDecoration(
                    color: colors.greyF5F9FF,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.close_rounded,
                    size: 20,
                    color: colors.grey5B5B5B,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildModernOperationGrid(
    List<ItemOperationModel> modelList,
    TreeNode node,
    BuildContext context, {
    String? deviceId,
    int? empowerType,
  }) {
    return Container(
      padding: EdgeInsets.all(spaces.leftLess),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 4, // 固定4列
          childAspectRatio: 0.85,
          crossAxisSpacing: spaces.j8,
          mainAxisSpacing: spaces.leftLess,
        ),
        itemCount: modelList.length,
        itemBuilder: (context, index) {
          ItemOperationModel model = modelList[index];
          return _buildModernOperationItem(model, node, context,
              deviceId: deviceId, empowerType: empowerType);
        },
      ),
    );
  }

  Widget _buildModernOperationItem(
    ItemOperationModel model,
    TreeNode node,
    BuildContext context, {
    String? deviceId,
    int? empowerType,
  }) {
    bool hasWarning = ((empowerType ?? -1) == 0 || (empowerType ?? -1) == 2) &&
        model.type == "1";

    return XBButton(
      onTap: () {
        selectOnTap(context, node, model.type,
            deviceId: deviceId, empowerType: empowerType);
      },
      child: Container(
        decoration: BoxDecoration(
          color: colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: hasWarning
                ? colors.red.withValues(alpha: 0.3)
                : colors.greyF5F9FF,
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: hasWarning
                  ? colors.red.withValues(alpha: 0.1)
                  : colors.black.withValues(alpha: 0.06),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Stack(
          children: [
            Container(
              width: double.infinity,
              height: double.infinity,
              padding: EdgeInsets.all(spaces.leftLess),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    padding: EdgeInsets.all(spaces.j8),
                    decoration: BoxDecoration(
                      color:
                          _getOperationColor(model.type).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: XBImage(
                      model.image,
                      width: 28,
                      height: 28,
                    ),
                  ),
                  SizedBox(height: spaces.j8),
                  Flexible(
                    child: Text(
                      model.name,
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        fontSize: fontSizes.s12,
                        color: colors.black4C,
                        fontWeight: fontWeights.medium,
                        height: 1.2,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            if (hasWarning)
              Positioned(
                top: spaces.j4,
                right: spaces.j4,
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: spaces.j4,
                    vertical: spaces.j4,
                  ),
                  decoration: BoxDecoration(
                    color: colors.red,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    syncText(empowerType),
                    style: TextStyle(
                      fontSize: fontSizes.s8,
                      color: colors.white,
                      fontWeight: fontWeights.medium,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Color _getOperationColor(String? type) {
    switch (type) {
      case "1": // 预览
        return colors.blue;
      case "2": // 回放
        return colors.green;
      case "3": // 设置
        return colors.orange;
      case "4": // 编辑
        return colors.blue;
      case "5": // 删除
        return colors.red;
      default:
        return colors.grey5B5B5B;
    }
  }

  Future<bool> checkNodeIsStore(String nodeId) async {
    bool isRetStore = false;
    await deviceTreeAPI.queryStoreNodeIdentify(nodeId).then((value) {
      NodeIdStoreModel? storeModel = value.firstOrNull;
      if (storeModel?.storeId?.isNotEmpty ?? false) {
        isRetStore = true;
      }
    }).catchError((error) {});
    return isRetStore;
  }

  Future<bool> checkNodeHasChildren(int nodeId) async {
    bool isRetStore = false;
    await deviceTreeAPI.queryChildDevice(nodeId).then((value) {
      isRetStore = value;
    }).catchError((error) {});
    return isRetStore;
  }
}
