import 'dart:io';
import 'package:bcloud/config/common_path.dart';
import 'package:bcloud/config/constant.dart';
import 'package:bcloud/generated/assets.gen.dart';
import 'package:bcloud/generated/l10n.dart';
import 'package:bcloud/pages/album/widget/album_bottom_tool_widget.dart';
import 'package:bcloud/pages/album/widget/album_media_item_widget.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/app_extension.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/xb_refresh_list_view.dart';
import 'package:bcloud/utils/device_status_permission.dart';
import 'package:bcloud/widget/x_appbar.dart';
import 'package:bcloud/widget/x_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:intl/intl.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:share_plus/share_plus.dart';
import 'package:xb_scaffold/xb_scaffold.dart';

class Album {
  String deviceName = ''; //设备名称或者序列号
  String yearMonthDayDate = ''; //设备日期 年月日
  String hourMinuteSecondTime = ''; //设备日期 只包含时分秒
  bool isSelected = false; //是否选中
  LocalMedia localMedia = LocalMedia(); //本地数据源
  Album(this.deviceName, this.yearMonthDayDate, this.hourMinuteSecondTime,
      isSelected, this.localMedia);
}

class AlbumPage extends StatefulWidget {
  const AlbumPage({Key? key}) : super(key: key);

  @override
  State<AlbumPage> createState() => _AlbumPageState();
}

class _AlbumPageState extends State<AlbumPage> {
  bool _isEditing = false;
  bool _isSelectedAll = false;

  ///album 数据源
  List<Album> _oDataList = [];

  ///选中的日期 ''的话代表全部日期
  String _kSelectedDate = '';

  ///选中的设备 ''的话代表全部设备
  String _kSelectedDevice = '';

  ///可选设备Map
  Map<String, int> _deviceMap = {};

  ///可选日期Map
  Map<DateTime, int> _dateMap = {};

  ///可选设备数组 map 由_deviceList 生成
  ///二维数据源 用于最终显示
  List<List<Album>> _doubleDataList = [];
  @override
  void initState() {
    super.initState();

    Future.delayed(Duration.zero, () {
      _load();
    });
  }

  _load() async {
    List<File> oListFile = await kGetLocalAlbumMedia();
    print('本地媒体数据源  开始===>');
    _oDataList = oListFile.map((e) {
      print(e.path);
      final localMedia = LocalMedia.fromFilePath(e.path);

      ///TODO...需要改为 序列号或者 设备名称 设备名称重设备列表中取
      final timeYearMonthDay = DateFormat('yyyy-MM-dd').format(localMedia.time);
      final hourMinuteSecondTime =
          DateFormat('HH:mm:ss').format(localMedia.time);
      return Album(localMedia.deviceId, timeYearMonthDay, hourMinuteSecondTime,
          false, localMedia);
    }).toList();

    ///根据期日排下序 倒序
    _oDataList.sort((Album a, Album b) {
      return b.localMedia.timeStr.compareTo(a.localMedia.timeStr);
    });

    print('本地媒体数据源  结束===>');
    _handleData();
  }

  ///处理生成二维数据源
  _handleData() {
    List<Album> tempList = [];

    ///1.筛选日期
    if (_kSelectedDate.isNotEmpty) {
      tempList = _oDataList
          .where((Album album) => album.yearMonthDayDate == _kSelectedDate)
          .toList();
    } else {
      tempList = List.from(_oDataList);
    }

    ///2.筛选设备
    if (_kSelectedDevice.isNotEmpty) {
      tempList = tempList
          .where((Album album) => album.deviceName == _kSelectedDevice)
          .toList();
    }

    ///3.根据日期分成二维数组
    _doubleDataList = _genDoubleDimensionList(tempList);

    ///4.生成可以筛选的设备map 和日期map
    for (Album album in _oDataList) {
      _deviceMap[album.deviceName] = 1;
      _dateMap[album.localMedia.time] = 1;
    }

    setState(() {});
  }

  ///生成二维数组
  _genDoubleDimensionList(List<Album> list) {
    //转成二维数组
    Map<String, List<Album>> dateToAlbumsMap = {};
    for (Album album in list) {
      if (!dateToAlbumsMap.containsKey(album.yearMonthDayDate)) {
        dateToAlbumsMap[album.yearMonthDayDate] = [];
      }
      dateToAlbumsMap[album.yearMonthDayDate]!.add(album);
    }

    List<List<Album>> doubleList = List.from(dateToAlbumsMap.values);

    ///不需要排序 因为之前_oDataList原始数据已经根据日期排过序了
    // //排序
    // //日期排序
    // doubleList
    //     .sort((List<Album> a, List<Album> b) => a[0].yearMonthDayDate.compareTo(b[0].yearMonthDayDate));
    // //时间排序
    // for (var element in doubleList) {
    //   element.sort((Album a, Album b) => a.time.compareTo(b.time));
    // }
    return doubleList;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: xAppBar(
          context: context,
          // title: TR.current.tr_Album,
          title: TR.current.tr_Album,
          actions: [
            GestureDetector(
              onTap: () {
                _onChangeEditingStatus();
              },
              child: Padding(
                padding: EdgeInsets.only(right: 16.w),
                child: Container(
                  height: double.infinity,
                  alignment: Alignment.center,
                  child: Text(
                    _isEditing ? TR.current.tr_Done : TR.current.tr_Common_Edit,
                    style: TextStyle(
                        color: colors.blue,
                        fontSize: 16.sp,
                        fontWeight: kFontWeight_Medium),
                  ),
                ),
              ),
            ),
          ]),
      backgroundColor: kColorHexStr('#F3F5F6'),
      body: Container(
        height: double.infinity,
        width: double.infinity,
        child: _doubleDataList.isNotEmpty
            ? Column(
                children: [
                  Expanded(child: _albumSectionView()),
                  Offstage(
                    offstage: !_isEditing,
                    child: AlbumBottomToolWidget(
                      doubleDataList: _doubleDataList,
                      isSelectedAll: _isSelectedAll,
                      onSelectAll: () {
                        ///总是全选 没有取消全选
                        _isSelectedAll = !_isSelectedAll;
                        _onSelectAll(_isSelectedAll);
                      },
                      onShare: _onShare,
                      onSave: _onSave,
                      onDelete: _onDelete,
                    ),
                  ),
                ],
              )
            : XBRefreshListView.noData(),
      ),
    );
  }

  Widget _albumSectionView() {
    List<Widget> list = [];
    for (final subList in _doubleDataList) {
      final Album album = subList[0];
      list.add(SliverList(
        delegate: SliverChildListDelegate(
          [
            Container(
              height: 43.w,
              width: double.infinity,
              alignment: Alignment.centerLeft,
              child: Row(
                children: [
                  // Assets.images.icAlbumTime.svg(
                  //     width: 12.w,
                  //     height: 12.w,
                  //     colorFilter: ColorFilter.mode(
                  //         kColorHexStr('#7E848D'), BlendMode.srcIn)),
                  SizedBox(
                    width: 16.w,
                  ),
                  Text(
                    album.yearMonthDayDate,
                    style: TextStyle(
                        color: colors.grey808080,
                        fontSize: 15.sp,
                        fontFamily: kFontFamily_PingFang_SC,
                        fontWeight: kFontWeight_Medium),
                  ),
                ],
              ),
            )
          ],
        ),
      ));
      list.add(
        SliverGrid(
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            childAspectRatio: 16 / 9.0,
            mainAxisSpacing: 1, //item上下之间的间距
            crossAxisSpacing: 1,
          ),
          delegate: SliverChildBuilderDelegate(
            (BuildContext context, int index) {
              return AlbumMediaItemWidget(
                ///要加key
                key: ValueKey(album.localMedia.fullFilePath),
                album: subList[index],
                isEditing: _isEditing,
                onUpdate: () {
                  setState(() {
                    ///用于触发AlbumBottomToolWidget内部按钮状态变化
                  });
                },
              );
            },
            childCount: subList.length,
          ),
        ),
      );
    }

    return CustomScrollView(
      slivers: list,
    );
  }

  _onSelectAll(bool isAll) {
    _isSelectedAll = isAll;
    for (var albums in _doubleDataList) {
      for (Album album in albums) {
        album.isSelected = _isSelectedAll;
      }
    }
    setState(() {});
  }

  _onShare() async {
    //遍历当前图片列表，找到选择的
    Map selectedAlbumMap = _queryCurrentHadBeSelectedAlbumMap();
    if (selectedAlbumMap.isEmpty) {
      toast(TR.current.tr_SelectItemTips);
      return;
    }

    List<XFile> willShareAlbums = [];
    int imageNum = 0;
    int videoNum = 0;
    for (Album album in selectedAlbumMap.keys) {
      willShareAlbums.add(XFile(album.localMedia.fullFilePath));
      if (album.localMedia.localFileType == LocalFileType.jpg ||
          album.localMedia.localFileType == LocalFileType.jpeg) {
        imageNum++;
      } else if (album.localMedia.localFileType == LocalFileType.mp4) {
        videoNum++;
      }
    }
    if (imageNum > 0 && videoNum > 0) {
      showDialog(
          context: context,
          builder: (BuildContext context) {
            return XDialog(
              title: TR.current.tr_ShareTips1,
            );
          });
      return;
    }
    if (videoNum > 1) {
      showDialog(
          context: context,
          builder: (BuildContext context) {
            return XDialog(
              title: TR.current.tr_ShareTips1,
            );
          });
      return;
    }
    if (imageNum > 5) {
      showDialog(
          context: context,
          builder: (BuildContext context) {
            return XDialog(
              title: TR.current.tr_ShareTipsImageMaxNum,
            );
          });
      return;
    }

    ShareResult result = await Share.shareXFiles(willShareAlbums);
    if (result.status == ShareResultStatus.success) {
      toast(TR.current.tr_ShareSuccess);
    }
  }

  _onSave() async {
    //遍历当前图片列表，找到选择的
    Map selectedAlbumMap = _queryCurrentHadBeSelectedAlbumMap();
    if (selectedAlbumMap.isEmpty) {
      toast(TR.current.tr_SelectItemTips);
      return;
    }

    await DeviceStatusAndPermission.instance.checkPermissionAlbum();
    if (DeviceStatusAndPermission.instance.isPermissionAlbum == false) {
      if (mounted) {
        showDialog(
            context: context,
            builder: (BuildContext context) {
              return XDialog(
                  subTitle: TR.current.tr_PleaseAllowVisitAllPhoto,
                  onConfirm: () {
                    openAppSettings();
                  });
            });
      }
      return;
    }

    if (mounted) {
      showDialog(
          context: context,
          builder: (BuildContext context) {
            return XDialog(
              title: TR.current.tr_Prompt,
              cancelText: TR.current.tr_Cancel,
              confirmText: TR.current.tr_Confirm,
              subTitle: TR.current.tr_SureSaveToAlbum,
              onConfirm: () async {
                showLoadingGlobal();
                for (Album album in selectedAlbumMap.keys) {
                  ///一个个保存
                  final result = await ImageGallerySaver.saveFile(
                      album.localMedia.fullFilePath);
                  // if (DeviceStatusAndPermission.instance.isPermissionLocalNet)
                }
                hideLoadingGlobal();
                toast(TR.current.tr_Downloaded); //已下载到相册
              },
            );
          });
    }
  }

  _onDelete() {
    //遍历当前图片列表，找到选择的
    Map selectedAlbumMap = _queryCurrentHadBeSelectedAlbumMap();
    if (selectedAlbumMap.isEmpty) {
      toast(TR.current.tr_SelectItemTips);
      return;
    }

    showDialog(
        context: context,
        builder: (BuildContext context) {
          return XDialog(
            title: TR.current.tr_Prompt,
            cancelText: TR.current.tr_Cancel,
            confirmText: TR.current.tr_Confirm,
            subTitle: TR.current.tr_SureItemDelete,
            onConfirm: () {
              ///过滤掉要删掉的元素
              _oDataList = _oDataList
                  .where((element) =>
                      selectedAlbumMap.containsKey(element) == false)
                  .toList();
              showLoadingGlobal();
              for (Album album in selectedAlbumMap.keys) {
                ///一个个删除
                final file = File(album.localMedia.fullFilePath);
                file.deleteSync();
              }
              hideLoadingGlobal();
              toast(TR.current.tr_SuccessfullyDeleted);

              ///重新整理数据
              _handleData();
            },
          );
        });
  }

  _onChangeEditingStatus() {
    /// 先取消全选
    _onSelectAll(false);
    setState(() {
      _isEditing = !_isEditing;
    });
  }

  ///查询已经被选择的album
  Map<Album, int> _queryCurrentHadBeSelectedAlbumMap() {
    final selectedAlbumMap = <Album, int>{};
    for (var albums in _doubleDataList) {
      for (Album album in albums) {
        if (album.isSelected) {
          selectedAlbumMap[album] = 1;
        }
      }
    }
    return selectedAlbumMap;
  }
}
