import 'package:bcloud/generated/l10n.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/app_extension.dart';
import 'package:bcloud/public/xb_scaffold_extennsion/xb_vm_refresh.dart';
import 'package:bcloud/widget/bc_refresh.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter/material.dart';
import 'package:xb_scaffold/xb_scaffold.dart';

class XBRefreshListView extends StatelessWidget {
  final XBVMRefresh vm;
  final bool needLoadMore;
  final bool needRefresh;
  final NullableIndexedWidgetBuilder itemBuilder;
  final IndexedWidgetBuilder? separatorBuilder;
  final ScrollController? controller;
  const XBRefreshListView(
      {required this.vm,
      required this.itemBuilder,
      this.separatorBuilder,
      this.needLoadMore = true,
      this.needRefresh = true,
      this.controller,
      super.key});

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        Widget child = vm.needNoData
            ? Container(
                // color: colors.randColor,
                height: constraints.maxHeight,
                child: noData())
            : ListView.separated(
                controller: controller,
                addAutomaticKeepAlives: false,
                itemCount: vm.itemCount,
                itemBuilder: itemBuilder,
                separatorBuilder: separatorBuilder ??
                    (ctx, index) {
                      return Container();
                    },
              );
        return BCRefresh(
            controller: vm.refreshController,
            onRefresh: vm.refresh,
            onLoadMore: vm.loadMore,
            needRefresh: needRefresh,
            needLoadMore: needLoadMore,
            initRefresh: vm.initialRefresh,
            child: child);
      },
    );
  }

  static Widget noData(
      {String? tip,
      double? fontSize,
      int? maxLines,
      double? imgWidth,
      TextAlign? titleAlign}) {
    return Container(
      color: Colors.transparent,
      alignment: const Alignment(0.0, -0.1),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 800),
        curve: Curves.easeOutCubic,
        padding: EdgeInsets.symmetric(horizontal: 40.w, vertical: 32.w),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.white.withAlpha(0),
              Colors.white.withAlpha(30),
              Colors.white.withAlpha(0),
            ],
            stops: const [0.0, 0.5, 1.0],
          ),
          borderRadius: BorderRadius.circular(24.r),
          border: Border.all(
            color: Colors.grey.withAlpha(20),
            width: 1,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 图标容器
            _buildIconContainer(imgWidth),
            SizedBox(height: 24.h),

            // 标题文本
            _buildTitleText(tip, fontSize, maxLines, titleAlign),
            SizedBox(height: 16.h),

            // 装饰性元素
            _buildDecorationDots(),
          ],
        ),
      ),
    );
  }

  static Widget _buildIconContainer(double? imgWidth) {
    final iconSize = imgWidth ?? 120.w;
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 1200),
      tween: Tween(begin: 0.0, end: 1.0),
      curve: Curves.elasticOut,
      builder: (context, value, child) {
        return Transform.scale(
          scale: value,
          child: Container(
            width: iconSize + 40.w,
            height: iconSize + 40.w,
            decoration: BoxDecoration(
              gradient: RadialGradient(
                center: Alignment.center,
                radius: 0.8,
                colors: [
                  const Color(0xFF2196F3).withAlpha(10),
                  const Color(0xFF00BCD4).withAlpha(5),
                  Colors.transparent,
                ],
                stops: const [0.0, 0.7, 1.0],
              ),
              shape: BoxShape.circle,
              border: Border.all(
                color: const Color(0xFF2196F3).withAlpha(20),
                width: 2,
              ),
            ),
            child: Center(
              child: Container(
                width: iconSize,
                height: iconSize,
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white,
                      Color(0xFFF8F9FA),
                    ],
                  ),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(8),
                      blurRadius: 20,
                      offset: const Offset(0, 8),
                    ),
                    BoxShadow(
                      color: const Color(0xFF2196F3).withAlpha(10),
                      blurRadius: 30,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Center(
                  child: XBImage(
                    images.ic_common_no_data_b,
                    width: iconSize * 0.6,
                    height: iconSize * 0.6,
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  static Widget _buildTitleText(
      String? tip, double? fontSize, int? maxLines, TextAlign? titleAlign) {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 1000),
      tween: Tween(begin: 0.0, end: 1.0),
      curve: Curves.easeOutCubic,
      builder: (context, value, child) {
        return Opacity(
          opacity: value,
          child: Transform.translate(
            offset: Offset(0, 20 * (1 - value)),
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white.withAlpha(80),
                    Colors.white.withAlpha(40),
                  ],
                ),
                borderRadius: BorderRadius.circular(16.r),
                border: Border.all(
                  color: Colors.grey.withAlpha(30),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(5),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Text(
                tip ?? TR.current.tr_NoData,
                textAlign: titleAlign ?? TextAlign.center,
                maxLines: maxLines ?? 3,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  fontSize: fontSize ?? 16.sp,
                  color: const Color(0xFF666666),
                  fontWeight: FontWeight.w500,
                  height: 1.4,
                  letterSpacing: 0.5,
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  static Widget _buildDecorationDots() {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 1500),
      tween: Tween(begin: 0.0, end: 1.0),
      curve: Curves.easeOutCubic,
      builder: (context, value, child) {
        return Opacity(
          opacity: value,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(3, (index) {
              return AnimatedContainer(
                duration: Duration(milliseconds: 600 + (index * 200)),
                margin: EdgeInsets.symmetric(horizontal: 4.w),
                width: 6.w,
                height: 6.w,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      const Color(0xFF2196F3).withAlpha((150 * value).toInt()),
                      const Color(0xFF00BCD4).withAlpha((100 * value).toInt()),
                    ],
                  ),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF2196F3)
                          .withAlpha((30 * value).toInt()),
                      blurRadius: 4,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
              );
            }),
          ),
        );
      },
    );
  }
}
